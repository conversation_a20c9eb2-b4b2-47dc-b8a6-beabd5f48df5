
'use client';

import { useState, type FormEvent } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { <PERSON>, PlusCircle, Edit3, Trash2, <PERSON><PERSON><PERSON>, <PERSON>ader2, User<PERSON>heck, UserX } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useForm, Controller, type SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Badge } from '@/components/ui/badge';

type UserRole = 'admin' | 'editor' | 'customer';
type UserStatus = 'active' | 'suspended';

const userSchema = z.object({
  name: z.string().min(3, { message: "الاسم يجب أن يكون 3 أحرف على الأقل" }),
  email: z.string().email({ message: "الرجاء إدخال بريد إلكتروني صحيح" }),
  role: z.enum(['admin', 'editor', 'customer'], { errorMap: () => ({ message: "الرجاء اختيار دور صحيح" }) }),
});

// For editing, we might not change password directly here or add status
const editUserSchema = userSchema.extend({
    status: z.enum(['active', 'suspended'], { errorMap: () => ({ message: "الرجاء اختيار حالة صحيحة" }) }),
});


interface SiteUser extends z.infer<typeof userSchema> {
  id: number;
  joinedDate: string;
  status: UserStatus;
}

const initialUsers: SiteUser[] = [
  { id: 1, name: 'المسؤول العام', email: '<EMAIL>', role: 'admin', joinedDate: '2023-01-15', status: 'active' },
  { id: 2, name: 'محرر محتوى', email: '<EMAIL>', role: 'editor', joinedDate: '2023-05-20', status: 'active' },
  { id: 3, name: 'أحمد العميل', email: '<EMAIL>', role: 'customer', joinedDate: '2024-02-10', status: 'active' },
  { id: 4, name: 'فاطمة الزبون', email: '<EMAIL>', role: 'customer', joinedDate: '2024-03-01', status: 'suspended' },
];

export default function AdminUsersPage() {
  const [users, setUsers] = useState<SiteUser[]>(initialUsers);
  // For adding new users, we'll simplify and not include status in the add form directly.
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<SiteUser | null>(null);
  const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false);
  const [deletingUserId, setDeletingUserId] = useState<number | null>(null);
  const [isSuspendAlertOpen, setIsSuspendAlertOpen] = useState(false);
  const [suspendingUser, setSuspendingUser] = useState<SiteUser | null>(null);

  const { toast } = useToast();

  const addUserForm = useForm<z.infer<typeof userSchema>>({
    resolver: zodResolver(userSchema),
    defaultValues: { name: '', email: '', role: 'customer'},
  });
  
  const editUserForm = useForm<z.infer<typeof editUserSchema>>({
    resolver: zodResolver(editUserSchema),
  });


  const handleAddNew = () => {
    addUserForm.reset({ name: '', email: '', role: 'customer' });
    setEditingUser(null);
    setIsAddModalOpen(true);
  };

  const handleEdit = (user: SiteUser) => {
    setEditingUser(user);
    editUserForm.reset({ name: user.name, email: user.email, role: user.role, status: user.status });
    setIsEditModalOpen(true);
  };

  const handleDelete = (id: number) => {
    setDeletingUserId(id);
    setIsDeleteAlertOpen(true);
  };
  
  const handleSuspendToggle = (user: SiteUser) => {
    setSuspendingUser(user);
    setIsSuspendAlertOpen(true);
  };

  const confirmDelete = () => {
    if (deletingUserId !== null) {
      setUsers(users.filter(user => user.id !== deletingUserId));
      toast({ title: "تم الحذف", description: "تم حذف المستخدم بنجاح." });
      setDeletingUserId(null);
    }
    setIsDeleteAlertOpen(false);
  };
  
  const confirmSuspendToggle = () => {
    if (suspendingUser) {
      const newStatus = suspendingUser.status === 'active' ? 'suspended' : 'active';
      setUsers(users.map(u => u.id === suspendingUser.id ? { ...u, status: newStatus } : u));
      toast({ title: "تم تحديث الحالة", description: `تم ${newStatus === 'active' ? 'تفعيل' : 'تعليق'} حساب المستخدم.`});
      setSuspendingUser(null);
    }
    setIsSuspendAlertOpen(false);
  };

  const onAddUserSubmit: SubmitHandler<z.infer<typeof userSchema>> = async (data) => {
    const newUser: SiteUser = { 
        id: Date.now(), 
        ...data, 
        joinedDate: new Date().toISOString().split('T')[0],
        status: 'active' // New users are active by default
    };
    setUsers([newUser, ...users]);
    toast({ title: "تمت الإضافة", description: "تمت إضافة المستخدم بنجاح." });
    setIsAddModalOpen(false);
    addUserForm.reset();
  };
  
  const onEditUserSubmit: SubmitHandler<z.infer<typeof editUserSchema>> = async (data) => {
     if (editingUser) {
      setUsers(users.map(user => user.id === editingUser.id ? { ...editingUser, ...data } : user));
      toast({ title: "تم التعديل", description: "تم تعديل بيانات المستخدم بنجاح." });
      setIsEditModalOpen(false);
      setEditingUser(null);
    }
    editUserForm.reset();
  };
  
  const getRoleText = (role: UserRole) => {
    if (role === 'admin') return 'مسؤول';
    if (role === 'editor') return 'محرر';
    return 'عميل';
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="h-6 w-6 text-primary" />
            <CardTitle>إدارة المستخدمين</CardTitle>
          </div>
          <Button onClick={handleAddNew}>
            <PlusCircle className="ml-2 h-4 w-4" />
            إضافة مستخدم جديد
          </Button>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>الاسم</TableHead>
                <TableHead>البريد الإلكتروني</TableHead>
                <TableHead>الدور</TableHead>
                <TableHead>تاريخ الانضمام</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead>الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.length > 0 ? users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell><Badge variant={user.role === 'admin' ? 'destructive' : user.role === 'editor' ? 'secondary' : 'outline'}>{getRoleText(user.role)}</Badge></TableCell>
                  <TableCell>{user.joinedDate}</TableCell>
                  <TableCell>
                    <Badge variant={user.status === 'active' ? 'default' : 'destructive'}>
                      {user.status === 'active' ? 'نشط' : 'معلق'}
                    </Badge>
                  </TableCell>
                  <TableCell className="space-x-1 rtl:space-x-reverse">
                    <Button variant="outline" size="icon" className="h-8 w-8" onClick={() => handleEdit(user)} title="تعديل">
                      <Edit3 className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon" className="h-8 w-8" onClick={() => handleSuspendToggle(user)} title={user.status === 'active' ? 'تعليق الحساب' : 'تفعيل الحساب'}>
                      {user.status === 'active' ? <UserX className="h-4 w-4 text-orange-500" /> : <UserCheck className="h-4 w-4 text-green-500" />}
                    </Button>
                    <Button variant="destructive" size="icon" className="h-8 w-8" onClick={() => handleDelete(user.id)} title="حذف">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              )) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center">لا يوجد مستخدمون لعرضهم.</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add User Modal */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <form onSubmit={addUserForm.handleSubmit(onAddUserSubmit)}>
            <DialogHeader>
              <DialogTitle>إضافة مستخدم جديد</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div>
                <Label htmlFor="add-name">الاسم</Label>
                <Input id="add-name" {...addUserForm.register("name")} className={`mt-1 ${addUserForm.formState.errors.name ? 'border-destructive' : ''}`} />
                {addUserForm.formState.errors.name && <p className="text-destructive text-sm mt-1">{addUserForm.formState.errors.name.message}</p>}
              </div>
              <div>
                <Label htmlFor="add-email">البريد الإلكتروني</Label>
                <Input id="add-email" type="email" {...addUserForm.register("email")} className={`mt-1 ${addUserForm.formState.errors.email ? 'border-destructive' : ''}`} />
                {addUserForm.formState.errors.email && <p className="text-destructive text-sm mt-1">{addUserForm.formState.errors.email.message}</p>}
              </div>
              <div>
                <Label htmlFor="add-role">الدور</Label>
                <Controller
                  name="role"
                  control={addUserForm.control}
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <SelectTrigger id="add-role" className={`mt-1 w-full ${addUserForm.formState.errors.role ? 'border-destructive' : ''}`}>
                        <SelectValue placeholder="اختر دورًا" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="customer">عميل</SelectItem>
                        <SelectItem value="editor">محرر</SelectItem>
                        <SelectItem value="admin">مسؤول</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                />
                 {addUserForm.formState.errors.role && <p className="text-destructive text-sm mt-1">{addUserForm.formState.errors.role.message}</p>}
              </div>
              <p className="text-sm text-muted-foreground">سيتم إنشاء كلمة مرور افتراضية ويمكن للمستخدم تغييرها لاحقًا.</p>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsAddModalOpen(false)}>إلغاء</Button>
              <Button type="submit" disabled={addUserForm.formState.isSubmitting}>
                {addUserForm.formState.isSubmitting && <Loader2 className="ml-2 h-4 w-4 animate-spin rtl:mr-2" />}
                إضافة المستخدم
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      
      {/* Edit User Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <form onSubmit={editUserForm.handleSubmit(onEditUserSubmit)}>
            <DialogHeader>
              <DialogTitle>تعديل بيانات المستخدم</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div>
                <Label htmlFor="edit-name">الاسم</Label>
                <Input id="edit-name" {...editUserForm.register("name")} className={`mt-1 ${editUserForm.formState.errors.name ? 'border-destructive' : ''}`} />
                {editUserForm.formState.errors.name && <p className="text-destructive text-sm mt-1">{editUserForm.formState.errors.name.message}</p>}
              </div>
              <div>
                <Label htmlFor="edit-email">البريد الإلكتروني</Label>
                <Input id="edit-email" type="email" {...editUserForm.register("email")} className={`mt-1 ${editUserForm.formState.errors.email ? 'border-destructive' : ''}`} />
                {editUserForm.formState.errors.email && <p className="text-destructive text-sm mt-1">{editUserForm.formState.errors.email.message}</p>}
              </div>
              <div>
                <Label htmlFor="edit-role">الدور</Label>
                <Controller
                  name="role"
                  control={editUserForm.control}
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <SelectTrigger id="edit-role" className={`mt-1 w-full ${editUserForm.formState.errors.role ? 'border-destructive' : ''}`}>
                        <SelectValue placeholder="اختر دورًا" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="customer">عميل</SelectItem>
                        <SelectItem value="editor">محرر</SelectItem>
                        <SelectItem value="admin">مسؤول</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                />
                {editUserForm.formState.errors.role && <p className="text-destructive text-sm mt-1">{editUserForm.formState.errors.role.message}</p>}
              </div>
               <div>
                <Label htmlFor="edit-status">الحالة</Label>
                <Controller
                  name="status"
                  control={editUserForm.control}
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <SelectTrigger id="edit-status" className={`mt-1 w-full ${editUserForm.formState.errors.status ? 'border-destructive' : ''}`}>
                        <SelectValue placeholder="اختر الحالة" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">نشط</SelectItem>
                        <SelectItem value="suspended">معلق</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                />
                {editUserForm.formState.errors.status && <p className="text-destructive text-sm mt-1">{editUserForm.formState.errors.status.message}</p>}
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsEditModalOpen(false)}>إلغاء</Button>
              <Button type="submit" disabled={editUserForm.formState.isSubmitting}>
                {editUserForm.formState.isSubmitting && <Loader2 className="ml-2 h-4 w-4 animate-spin rtl:mr-2" />}
                حفظ التعديلات
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteAlertOpen} onOpenChange={setIsDeleteAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد من حذف المستخدم؟</AlertDialogTitle>
            <AlertDialogDescription>
              سيتم حذف هذا المستخدم بشكل دائم. لا يمكن التراجع عن هذا الإجراء.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete}>نعم، قم بالحذف</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Suspend/Unsuspend Confirmation Dialog */}
        <AlertDialog open={isSuspendAlertOpen} onOpenChange={setIsSuspendAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
                {suspendingUser?.status === 'active' ? 'هل أنت متأكد من تعليق حساب المستخدم؟' : 'هل أنت متأكد من تفعيل حساب المستخدم؟'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {suspendingUser?.status === 'active' 
                ? 'سيتم تعليق حساب هذا المستخدم ولن يتمكن من تسجيل الدخول.' 
                : 'سيتم تفعيل حساب هذا المستخدم وسيتمكن من تسجيل الدخول مرة أخرى.'}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction onClick={confirmSuspendToggle}>
                {suspendingUser?.status === 'active' ? 'نعم، قم بالتعليق' : 'نعم، قم بالتفعيل'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

    