const { PrismaClient, ContentType } = require('@prisma/client');
const { hash } = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  // Default contact information
  await prisma.setting.upsert({
    where: { name: 'phone' },
    create: {
      name: 'phone',
      value: '+966512345678',
      type: 'TEXT',
      group: 'CONTACT'
    },
    update: {
      value: '+966512345678'
    }
  });

  await prisma.setting.upsert({
    where: { name: 'email' },
    create: {
      name: 'email',
      value: '<EMAIL>',
      type: 'TEXT',
      group: 'CONTACT'
    },
    update: {
      value: '<EMAIL>'
    }
  });

  await prisma.setting.upsert({
    where: { name: 'address' },
    create: {
      name: 'address',
      value: 'شارع الملك عبدالعزيز، حي العزيزية، مكة المكرمة، المملكة العربية السعودية',
      type: 'TEXT',
      group: 'CONTACT'
    },
    update: {
      value: 'شارع الملك عبدالعزيز، حي العزيزية، مكة المكرمة، المملكة العربية السعودية'
    }
  });

  await prisma.setting.upsert({
    where: { name: 'workingHours' },
    create: {
      name: 'workingHours',
      value: 'من السبت إلى الخميس: 9:00 صباحاً - 5:00 مساءً',
      type: 'TEXT',
      group: 'CONTACT'
    },
    update: {
      value: 'من السبت إلى الخميس: 9:00 صباحاً - 5:00 مساءً'
    }
  });

  await prisma.setting.upsert({
    where: { name: 'whatsapp' },
    create: {
      name: 'whatsapp',
      value: '+966512345678',
      type: 'TEXT',
      group: 'CONTACT'
    },
    update: {
      value: '+966512345678'
    }
  });

  // Social Media Links
  await prisma.setting.upsert({
    where: { name: 'facebook' },
    create: {
      name: 'facebook',
      value: 'https://facebook.com/ithaar',
      type: 'TEXT',
      group: 'SOCIAL'
    },
    update: {
      value: 'https://facebook.com/ithaar'
    }
  });

  await prisma.setting.upsert({
    where: { name: 'twitter' },
    create: {
      name: 'twitter',
      value: 'https://twitter.com/ithaar',
      type: 'TEXT',
      group: 'SOCIAL'
    },
    update: {
      value: 'https://twitter.com/ithaar'
    }
  });

  await prisma.setting.upsert({
    where: { name: 'instagram' },
    create: {
      name: 'instagram',
      value: 'https://instagram.com/ithaar',
      type: 'TEXT',
      group: 'SOCIAL'
    },
    update: {
      value: 'https://instagram.com/ithaar'
    }
  });

  await prisma.setting.upsert({
    where: { name: 'youtube' },
    create: {
      name: 'youtube',
      value: 'https://youtube.com/ithaar',
      type: 'TEXT',
      group: 'SOCIAL'
    },
    update: {
      value: 'https://youtube.com/ithaar'
    }
  });

  await prisma.setting.upsert({
    where: { name: 'website' },
    create: {
      name: 'website',
      value: 'https://ithaar.com',
      type: 'TEXT',
      group: 'SOCIAL'
    },
    update: {
      value: 'https://ithaar.com'
    }
  });

  // Map Location
  await prisma.setting.upsert({
    where: { name: 'location' },
    create: {
      name: 'location',
      value: JSON.stringify({
        lat: 21.422510,
        lng: 39.826168,
        zoom: 15
      }),
      type: 'JSON',
      group: 'MAP'
    },
    update: {
      value: JSON.stringify({
        lat: 21.422510,
        lng: 39.826168,
        zoom: 15
      })
    }
  });

  // Create admin user
  const hashedPassword = await hash('admin123', 12);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'ADMIN',
    },
  });

  console.log({ admin });

  // Add About page content
  await prisma.content.upsert({
    where: {
      id: 'about-page'
    },
    update: {},
    create: {
      id: 'about-page',
      type: ContentType.PAGE,
      title: 'about',
      content: JSON.stringify({
        backgroundImage: '/images/about-header.jpg',
        sections: {
          mission: 'نسعى في إيثار لتقديم خدمات حج وعمرة متميزة تتسم بالاحترافية والمصداقية.',
          vision: 'أن نكون الشركة الرائدة في مجال خدمات الحج والعمرة على مستوى المملكة.',
          values: [
            'الإخلاص',
            'الأمانة',
            'الجودة',
            'الضيافة'
          ]
        }
      })
    }
  });

  console.log('Database has been seeded. 🌱');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 