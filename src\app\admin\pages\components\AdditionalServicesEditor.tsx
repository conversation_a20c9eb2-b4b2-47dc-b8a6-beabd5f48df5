import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { PlusCircle, Trash2 } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export interface AdditionalService {
  id: string;
  name: string;
  description: string;
  icon: string;
}

interface AdditionalServicesEditorProps {
  services: AdditionalService[];
  onSave: (services: AdditionalService[]) => void;
}

const availableIcons = [
  { value: 'FileText', label: 'مستند' },
  { value: 'Plane', label: 'طائرة' },
  { value: 'Hotel', label: 'فندق' },
  { value: 'Car', label: 'سيارة' },
  { value: 'Utensils', label: 'طعام' },
  { value: 'Users', label: 'مرشدون' },
  { value: 'BookOpen', label: 'كتاب' },
  { value: 'Crown', label: 'تاج' },
  { value: 'ShieldCheck', label: 'درع' },
];

export default function AdditionalServicesEditor({ services: initialServices, onSave }: AdditionalServicesEditorProps) {
  const [services, setServices] = useState<AdditionalService[]>(initialServices);

  const addService = () => {
    const newService: AdditionalService = {
      id: Date.now().toString(),
      name: '',
      description: '',
      icon: 'FileText',
    };
    setServices([...services, newService]);
  };

  const updateService = (index: number, field: keyof AdditionalService, value: string) => {
    const updatedServices = [...services];
    updatedServices[index] = {
      ...updatedServices[index],
      [field]: value,
    };
    setServices(updatedServices);
  };

  const removeService = (index: number) => {
    const updatedServices = [...services];
    updatedServices.splice(index, 1);
    setServices(updatedServices);
  };

  const handleSave = () => {
    onSave(services);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">الخدمات الإضافية</h3>
        <Button onClick={addService}>
          <PlusCircle className="ml-2 h-4 w-4" />
          إضافة خدمة جديدة
        </Button>
      </div>

      <div className="grid gap-4">
        {services.map((service, index) => (
          <Card key={service.id} className="relative">
            <Button
              variant="destructive"
              size="icon"
              className="absolute left-2 top-2"
              onClick={() => removeService(index)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>

            <CardContent className="pt-8">
              <div className="grid gap-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>اسم الخدمة</Label>
                    <Input
                      value={service.name}
                      onChange={(e) => updateService(index, 'name', e.target.value)}
                      placeholder="مثال: إصدار التأشيرات"
                    />
                  </div>
                  <div>
                    <Label>الأيقونة</Label>
                    <Select
                      value={service.icon}
                      onValueChange={(value) => updateService(index, 'icon', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="اختر أيقونة" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableIcons.map((icon) => (
                          <SelectItem key={icon.value} value={icon.value}>
                            {icon.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label>وصف الخدمة</Label>
                  <Textarea
                    value={service.description}
                    onChange={(e) => updateService(index, 'description', e.target.value)}
                    placeholder="اكتب وصفاً مختصراً للخدمة..."
                    rows={2}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="flex justify-end">
        <Button onClick={handleSave} className="w-full md:w-auto">
          حفظ التغييرات
        </Button>
      </div>
    </div>
  );
} 