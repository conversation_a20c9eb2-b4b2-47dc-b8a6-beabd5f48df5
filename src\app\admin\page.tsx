import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, Newspaper, Image as ImageIcon, Video, BarChart3, Eye } from "lucide-react";

const StatCard = ({ title, value, icon, description }: { title: string, value: string, icon: React.ReactNode, description: string }) => (
  <Card className="shadow-sm hover:shadow-md transition-shadow">
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      {icon}
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      <p className="text-xs text-muted-foreground">{description}</p>
    </CardContent>
  </Card>
);

export default function AdminDashboardPage() {
  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">لوحة التحكم الرئيسية</h1>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard 
          title="إجمالي الزوار (آخر 30 يومًا)" 
          value="1,234" 
          icon={<Eye className="h-5 w-5 text-muted-foreground" />} 
          description="+20.1% من الشهر الماضي" 
        />
        <StatCard 
          title="عدد الحجوزات" 
          value="56" 
          icon={<Users className="h-5 w-5 text-muted-foreground" />} 
          description="+15 هذا الشهر" 
        />
        <StatCard 
          title="مقالات الأخبار المنشورة" 
          value="12" 
          icon={<Newspaper className="h-5 w-5 text-muted-foreground" />} 
          description="2 جديدة هذا الأسبوع" 
        />
        <StatCard 
          title="عناصر الوسائط" 
          value="150" 
          icon={<ImageIcon className="h-5 w-5 text-muted-foreground" />} 
          description="50 صورة, 10 فيديوهات" 
        />
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>نظرة عامة على النشاط</CardTitle>
            <CardDescription>ملخص سريع للأنشطة الأخيرة في الموقع.</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Placeholder for a chart or activity feed */}
            <div className="h-64 bg-muted flex items-center justify-center rounded-md">
              <BarChart3 className="h-16 w-16 text-muted-foreground" />
              <p className="ml-4 text-muted-foreground">بيانات النشاط ستعرض هنا</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>أحدث الحجوزات</CardTitle>
            <CardDescription>قائمة بآخر 5 حجوزات تمت.</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-3">
              {["أحمد علي (باقة الحج المميزة)", "فاطمة حسن (باقة العمرة الذهبية)", "محمد عبدالله (باقة الحج الاقتصادية)", "سارة خالد (باقة العمرة الفضية)", "يوسف إبراهيم (باقة الحج المميزة)"].map((booking, index) => (
                <li key={index} className="flex justify-between items-center p-2 bg-muted/50 rounded-md">
                  <span>{booking}</span>
                  <span className="text-xs text-muted-foreground">منذ {index + 1} أيام</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
