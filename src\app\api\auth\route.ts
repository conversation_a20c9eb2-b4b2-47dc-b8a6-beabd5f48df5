import { NextResponse } from 'next/server';
import { sign } from 'jsonwebtoken';
import { compare } from 'bcryptjs';
import { prisma } from '@/lib/prisma';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json();
    console.log('Login attempt:', email);

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email }
    });

    if (!user) {
      console.log('User not found');
      return NextResponse.json(
        { success: false, error: 'البريد الإلكتروني أو كلمة المرور غير صحيحة' },
        { status: 401 }
      );
    }

    // Log password info (DO NOT LOG IN PRODUCTION)
    console.log('Found user:', {
      id: user.id,
      email: user.email,
      role: user.role,
      passwordLength: user.password.length,
    });

    // Verify password
    try {
      const validPassword = await compare(password, user.password);
      console.log('Password validation result:', validPassword);

      if (!validPassword) {
        console.log('Invalid password');
        return NextResponse.json(
          { success: false, error: 'البريد الإلكتروني أو كلمة المرور غير صحيحة' },
          { status: 401 }
        );
      }
    } catch (error) {
      console.error('Password comparison error:', error);
      return NextResponse.json(
        { success: false, error: 'حدث خطأ في التحقق من كلمة المرور' },
        { status: 500 }
      );
    }

    // Check if user is admin
    if (user.role !== 'ADMIN') {
      console.log('User is not admin:', user.role);
      return NextResponse.json(
        { success: false, error: 'غير مصرح لك بالدخول' },
        { status: 403 }
      );
    }

    // Generate JWT token
    const token = sign(
      { userId: user.id, email: user.email, role: user.role },
      JWT_SECRET,
      { expiresIn: '7d' }
    );
    console.log('Generated token');

    // Create response with token
    const response = NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role
      }
    });

    // Set auth cookie
    response.cookies.set({
      name: 'auth',
      value: token,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7 // 7 days
    });

    console.log('Login successful for:', email);
    return response;

  } catch (error: any) {
    console.error('Auth Error:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في تسجيل الدخول' },
      { status: 500 }
    );
  }
}

export async function DELETE() {
  const response = NextResponse.json({ success: true });
  response.cookies.delete('auth');
  return response;
}