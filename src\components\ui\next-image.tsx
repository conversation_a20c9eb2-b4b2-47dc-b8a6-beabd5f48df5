'use client';

import { useState } from 'react';
import Image, { ImageProps } from 'next/image';
import { cn } from '@/lib/utils';

interface NextImageProps extends Omit<ImageProps, 'onError' | 'alt'> {
  fallbackSrc?: string;
  alt: string;
  wrapperClassName?: string;
}

export default function NextImage({ 
  src, 
  alt, 
  className,
  wrapperClassName,
  fallbackSrc = "https://placehold.co/600x400?text=صورة+غير+متوفرة",
  ...props 
}: NextImageProps) {
  const [error, setError] = useState(false);

  return (
    <div className={cn('relative', wrapperClassName)}>
      <Image
        src={error ? fallbackSrc : src}
        alt={alt}
        className={cn(
          'transition-opacity duration-300',
          error ? 'opacity-60' : 'opacity-100',
          className
        )}
        onError={() => setError(true)}
        {...props}
      />
      {error && (
        <div className="absolute inset-0 flex items-center justify-center text-sm text-center text-muted-foreground p-2">
          تعذر تحميل الصورة
        </div>
      )}
    </div>
  );
}