'use client';

import { useState, type FormEvent, useEffect } from 'react';
import Image from 'next/image';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea'; // Assuming you want rich text later, but for now Textarea
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { FileText, PlusCircle, Edit3, Trash2, Loader2, FileEdit } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useForm, Controller, type SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import dynamic from 'next/dynamic';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import type { Package } from './components/PackageEditor';
import type { AdditionalService } from './components/AdditionalServicesEditor';
import { initialHajjPackages, initialUmrahPackages, initialAdditionalServices } from '@/lib/initial-data';
import { PageBackgroundSelector } from '@/components/admin/PageBackgroundSelector';

const pageSchema = z.object({
  title: z.string().min(3, { message: "عنوان الصفحة يجب أن يكون 3 أحرف على الأقل" }),
  slug: z.string().min(3, { message: "الرابط (slug) يجب أن يكون 3 أحرف على الأقل" }).regex(/^[a-z0-9-]+$/, { message: "الرابط يمكن أن يحتوي فقط على أحرف صغيرة وأرقام وشرطات" }),
  content: z.string().min(10, { message: "محتوى الصفحة يجب أن يكون 10 أحرف على الأقل" }),
});

type SitePage = z.infer<typeof pageSchema> & {
  id: number;
  lastModified: string;
};

const initialSitePages: SitePage[] = [
  { id: 1, title: 'سياسة الخصوصية', slug: 'privacy-policy', content: 'هذه هي سياسة الخصوصية الخاصة بموقعنا...', lastModified: '2024-03-10' },
  { id: 2, title: 'شروط الخدمة', slug: 'terms-of-service', content: 'شروط وأحكام استخدام الموقع والخدمات...', lastModified: '2024-03-01' },
];

interface PageSection {
  id: string;
  title: string;
  type: 'text' | 'list' | 'packages' | 'contact' | 'map' | 'schedule';
}

interface PageTemplate {
  title: string;
  sections: PageSection[];
}

interface PageContent {
  backgroundImage?: string;
}

const PackageEditor = dynamic(() => import('./components/PackageEditor'), { ssr: false });
const AdditionalServicesEditor = dynamic(() => import('./components/AdditionalServicesEditor'), { ssr: false });

interface ServicePageContent {
  hajjPackages: Package[];
  umrahPackages: Package[];
  additionalServices: AdditionalService[];
  pageHeader?: {
    backgroundImage: string;
  };
}

const pageTemplates: Record<string, PageTemplate & { content?: PageContent }> = {
  about: {
    title: 'من نحن',
    sections: [
      { id: 'mission', title: 'رسالتنا', type: 'text' },
      { id: 'vision', title: 'رؤيتنا', type: 'text' },
      { id: 'values', title: 'قيمنا', type: 'list' },
      { id: 'whyUs', title: 'لماذا تختار إيثار', type: 'list' },
    ],
    content: {
      backgroundImage: ''
    }
  },
  media: {
    title: 'مكتبة الوسائط',
    sections: [],
    content: {
      backgroundImage: ''
    }
  },
  testimonials: {
    title: 'آراء العملاء',
    sections: [],
    content: {
      backgroundImage: ''
    }
  },
  news: {
    title: 'آخر الأخبار',
    sections: [],
    content: {
      backgroundImage: ''
    }
  },
  faq: {
    title: 'الأسئلة الشائعة',
    sections: [],
    content: {
      backgroundImage: ''
    }
  },
  contact: {
    title: 'تواصل معنا',
    sections: [
      { id: 'contactInfo', title: 'معلومات الاتصال', type: 'contact' },
      { id: 'location', title: 'موقعنا', type: 'map' },
      { id: 'workingHours', title: 'ساعات العمل', type: 'schedule' },
    ],
    content: {
      backgroundImage: ''
    }
  },
  services: {
    title: 'خدماتنا',
    sections: [
      { id: 'hajjPackages', title: 'باقات الحج', type: 'packages' },
      { id: 'umrahPackages', title: 'باقات العمرة', type: 'packages' },
      { id: 'additionalServices', title: 'خدمات إضافية', type: 'list' },
    ],
    content: {
      backgroundImage: ''
    }
  }
};

export default function AdminPagesPage() {
  const [sitePages, setSitePages] = useState<SitePage[]>(initialSitePages);
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingPage, setEditingPage] = useState<SitePage | null>(null);
  const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false);
  const [deletingPageId, setDeletingPageId] = useState<number | null>(null);
  const { toast } = useToast();
  const [serviceContent, setServiceContent] = useState<ServicePageContent>({
    hajjPackages: initialHajjPackages,
    umrahPackages: initialUmrahPackages,
    additionalServices: initialAdditionalServices
  });
  const [pageContents, setPageContents] = useState<Record<string, PageContent>>({});

  // Load saved service content on mount
  useEffect(() => {
    const loadServiceContent = async () => {
      try {
        const response = await fetch('/api/services');
        if (response.ok) {
          const data = await response.json();
          setServiceContent(data);
        }
      } catch (error) {
        console.error('Error loading service content:', error);
      }
    };

    loadServiceContent();
  }, []);

  // Load saved page contents on mount
  useEffect(() => {
    const loadPageContents = async () => {
      try {
        const response = await fetch('/api/pages');
        if (response.ok) {
          const data = await response.json();
          setPageContents(data);
        }
      } catch (error) {
        console.error('Error loading page contents:', error);
      }
    };

    loadPageContents();
  }, []);

  const { register, handleSubmit, control, reset, setValue, formState: { errors, isSubmitting } } = useForm<z.infer<typeof pageSchema>>({
    resolver: zodResolver(pageSchema),
    defaultValues: { title: '', slug: '', content: '' },
  });

  const handleAddNew = () => {
    reset({ title: '', slug: '', content: '' });
    setEditingPage(null);
    setIsAddModalOpen(true);
  };

  const handleEdit = (page: SitePage) => {
    setEditingPage(page);
    setValue('title', page.title);
    setValue('slug', page.slug);
    setValue('content', page.content);
    setIsEditModalOpen(true);
  };

  const handleDelete = (id: number) => {
    setDeletingPageId(id);
    setIsDeleteAlertOpen(true);
  };

  const confirmDelete = () => {
    if (deletingPageId !== null) {
      setSitePages(sitePages.filter(p => p.id !== deletingPageId));
      toast({ title: "تم الحذف", description: "تم حذف الصفحة بنجاح." });
      setDeletingPageId(null);
    }
    setIsDeleteAlertOpen(false);
  };

  const onSubmit: SubmitHandler<z.infer<typeof pageSchema>> = async (data) => {
    try {
      const pageData = { ...data, slug: data.slug.toLowerCase().replace(/\s+/g, '-') };
      
      // Check if slug already exists
      const slugExists = sitePages.some(p => 
        p.slug === pageData.slug && 
        (!editingPage || p.id !== editingPage.id)
      );
      
      if (slugExists) {
        toast({ 
          title: "خطأ", 
          description: "هذا الرابط مستخدم بالفعل، الرجاء اختيار رابط آخر.",
          variant: "destructive"
        });
        return;
      }

      if (editingPage) {
        // Update existing page
        setSitePages(sitePages.map(p => 
          p.id === editingPage.id 
            ? { ...p, ...pageData, lastModified: new Date().toISOString().split('T')[0] } 
            : p
        ));
        toast({ title: "تم التعديل", description: "تم تعديل الصفحة بنجاح." });
        setIsEditModalOpen(false);
      } else {
        // Add new page
        const newPage: SitePage = { 
          id: Date.now(), 
          ...pageData, 
          lastModified: new Date().toISOString().split('T')[0] 
        };
        setSitePages(prevPages => [newPage, ...prevPages]);
        toast({ title: "تمت الإضافة", description: "تمت إضافة الصفحة بنجاح." });
        setIsAddModalOpen(false);
      }
      reset();
    } catch (error) {
      console.error('Error submitting page:', error);
      toast({ 
        title: "خطأ", 
        description: "حدث خطأ أثناء حفظ الصفحة. الرجاء المحاولة مرة أخرى.",
        variant: "destructive"
      });
    }
  };

  const handleHajjPackagesUpdate = async (packages: Package[]) => {
    try {
      setServiceContent(prev => {
        const newContent = { ...prev, hajjPackages: packages };
        // Save to API
        fetch('/api/services', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(newContent),
        });
        return newContent;
      });
      
      toast({ title: "تم التحديث", description: "تم تحديث باقات الحج بنجاح." });
    } catch (error) {
      console.error('Error saving hajj packages:', error);
      toast({ 
        title: "خطأ في الحفظ", 
        description: "حدث خطأ أثناء حفظ التغييرات",
        variant: "destructive"
      });
    }
  };

  const handleUmrahPackagesUpdate = async (packages: Package[]) => {
    try {
      setServiceContent(prev => {
        const newContent = { ...prev, umrahPackages: packages };
        // Save to API
        fetch('/api/services', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(newContent),
        });
        return newContent;
      });
      
      toast({ title: "تم التحديث", description: "تم تحديث باقات العمرة بنجاح." });
    } catch (error) {
      console.error('Error saving umrah packages:', error);
      toast({ 
        title: "خطأ في الحفظ", 
        description: "حدث خطأ أثناء حفظ التغييرات",
        variant: "destructive"
      });
    }
  };

  const handleAdditionalServicesUpdate = async (services: AdditionalService[]) => {
    try {
      setServiceContent(prev => {
        const newContent = { ...prev, additionalServices: services };
        // Save to API
        fetch('/api/services', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(newContent),
        });
        return newContent;
      });
      
      toast({ title: "تم التحديث", description: "تم تحديث الخدمات الإضافية بنجاح." });
    } catch (error) {
      console.error('Error saving additional services:', error);
      toast({ 
        title: "خطأ في الحفظ", 
        description: "حدث خطأ أثناء حفظ التغييرات",
        variant: "destructive"
      });
    }
  };

  const handleBackgroundImageSelect = async (pageKey: string, imageUrl: string) => {
    try {
      console.log('Updating background image for page:', pageKey);
      console.log('Current page content:', pageContents[pageKey]);
      console.log('New image URL:', imageUrl);

      const response = await fetch('/api/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pageKey,
          content: {
            ...pageContents[pageKey],
            backgroundImage: imageUrl
          }
        }),
      });

      if (response.ok) {
        const updatedContent = await response.json();
        console.log('Server response:', updatedContent);

        setPageContents(prev => ({
          ...prev,
          [pageKey]: {
            ...prev[pageKey],
            backgroundImage: imageUrl
          }
        }));

        toast({
          title: "تم التحديث",
          description: "تم تحديث صورة الخلفية بنجاح.",
        });
      } else {
        console.error('Server error:', await response.text());
        throw new Error('Failed to update background image');
      }
    } catch (error) {
      console.error('Error updating background image:', error);
      toast({
        title: "خطأ في التحديث",
        description: "حدث خطأ أثناء تحديث صورة الخلفية.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <FileEdit className="h-6 w-6 text-primary" />
            <CardTitle>إدارة صفحات الموقع</CardTitle>
          </div>
          <Button onClick={handleAddNew}>
            <PlusCircle className="ml-2 h-4 w-4" />
            إضافة صفحة جديدة
          </Button>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6">
            {Object.entries(pageTemplates).map(([key, template]) => (
              <Card key={key} className="p-6">
                <CardHeader className="px-0 pt-0">
                  <CardTitle className="text-xl">{template.title}</CardTitle>
                  <CardDescription>قم بتعديل محتوى صفحة {template.title}</CardDescription>
                </CardHeader>
                <CardContent className="px-0">
                  {key === 'services' ? (
                    <Tabs defaultValue="hajj" className="w-full">
                      <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="hajj">باقات الحج</TabsTrigger>
                        <TabsTrigger value="umrah">باقات العمرة</TabsTrigger>
                        <TabsTrigger value="additional">خدمات إضافية</TabsTrigger>
                      </TabsList>
                      <TabsContent value="hajj" className="space-y-6">
                        <Card className="p-4">
                          <CardHeader className="px-0 pt-0">
                            <CardTitle className="text-lg">رأس الصفحة</CardTitle>
                            <CardDescription>
                              تخصيص خلفية رأس صفحة الخدمات
                              <br />
                              <span className="text-sm text-muted-foreground">
                                المقاس المثالي: 1200×400 بكسل
                              </span>
                            </CardDescription>
                          </CardHeader>
                          <CardContent className="px-0">
                            <div className="space-y-4">
                              <div>
                                <Label>صورة الخلفية</Label>
                                <Input
                                  type="file"
                                  accept="image/*"
                                  onChange={async (e) => {
                                    const file = e.target.files?.[0];
                                    if (file) {
                                      const formData = new FormData();
                                      formData.append('file', file);
                                      formData.append('title', 'خلفية صفحة الخدمات');
                                      formData.append('description', 'صورة خلفية لرأس صفحة الخدمات');
                                      formData.append('type', 'IMAGE');
                                      formData.append('category', 'OTHER');

                                      try {
                                        const response = await fetch('/api/media/upload', {
                                          method: 'POST',
                                          body: formData,
                                        });
                                        
                                        const data = await response.json();
                                        
                                        if (data.url) {
                                          setServiceContent(prev => ({
                                            ...prev,
                                            pageHeader: {
                                              ...prev.pageHeader,
                                              backgroundImage: data.url
                                            }
                                          }));
                                          
                                          // Save to API
                                          fetch('/api/services', {
                                            method: 'POST',
                                            headers: {
                                              'Content-Type': 'application/json',
                                            },
                                            body: JSON.stringify({
                                              ...serviceContent,
                                              pageHeader: {
                                                backgroundImage: `/uploads/${data.url.split('/').pop()}`
                                              }
                                            }),
                                          });
                                        }
                                      } catch (error) {
                                        console.error('Error uploading file:', error);
                                      }
                                    }
                                  }}
                                />
                              </div>
                              {serviceContent.pageHeader?.backgroundImage && (
                                <div className="relative w-full h-32 bg-muted rounded-lg overflow-hidden mt-4">
                                  <p className="text-sm text-muted-foreground mb-2">المعاينة:</p>
                                  <Image
                                    src={serviceContent.pageHeader.backgroundImage}
                                    alt="صورة خلفية رأس الصفحة"
                                    fill
                                    className="object-cover"
                                  />
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                        <PackageEditor
                          packages={serviceContent.hajjPackages}
                          onSave={handleHajjPackagesUpdate}
                          type="hajj"
                        />
                      </TabsContent>
                      <TabsContent value="umrah">
                        <PackageEditor
                          packages={serviceContent.umrahPackages}
                          onSave={handleUmrahPackagesUpdate}
                          type="umrah"
                        />
                      </TabsContent>
                      <TabsContent value="additional">
                        <AdditionalServicesEditor
                          services={serviceContent.additionalServices}
                          onSave={handleAdditionalServicesUpdate}
                        />
                      </TabsContent>
                    </Tabs>
                  ) : (
                    <div className="space-y-6">
                      {template.content && (
                        <PageBackgroundSelector
                          title="صورة خلفية رأس الصفحة"
                          currentImage={pageContents[key]?.backgroundImage || template.content.backgroundImage}
                          onSelect={(imageUrl) => handleBackgroundImageSelect(key, imageUrl)}
                        />
                      )}
                      {template.sections.map((section) => (
                        <div key={section.id} className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium">{section.title}</h4>
                            <p className="text-sm text-muted-foreground">
                              {getContentTypeDescription(section.type)}
                            </p>
                          </div>
                          <Button variant="outline" onClick={() => handleEdit({
                            id: 0,
                            title: section.title,
                            slug: key,
                            content: '',
                            lastModified: new Date().toISOString().split('T')[0]
                          })}>
                            تعديل المحتوى
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Edit Content Dialog */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>تعديل محتوى {editingPage?.title}</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid gap-6">
              <div>
                <Label>المحتوى</Label>
                <Controller
                  name="content"
                  control={control}
                  render={({ field }) => (
                    <div className="mt-2">
                      <Textarea
                        {...field}
                        rows={10}
                        className="font-mono"
                        placeholder="أدخل المحتوى هنا..."
                      />
                    </div>
                  )}
                />
              </div>
              
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">معاينة المحتوى</h4>
                <div className="prose prose-sm max-w-none">
                  {/* Add content preview here */}
                </div>
              </div>
            </div>
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsEditModalOpen(false)}>
                إلغاء
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="ml-2 h-4 w-4 animate-spin" />}
                حفظ التغييرات
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Add New Page Dialog */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>إضافة صفحة جديدة</DialogTitle>
            <DialogDescription>
              قم بإدخال معلومات الصفحة الجديدة
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid gap-6">
              <div>
                <Label htmlFor="title">عنوان الصفحة</Label>
                <Input
                  id="title"
                  {...register('title')}
                  className="mt-2"
                  placeholder="أدخل عنوان الصفحة"
                  dir="rtl"
                />
                {errors.title && (
                  <p className="text-sm text-red-500 mt-1">{errors.title.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="slug">
                  رابط الصفحة (URL)
                  <span className="text-sm text-muted-foreground mr-1">
                    (يجب أن يحتوي على أحرف إنجليزية صغيرة وأرقام وشرطات فقط)
                  </span>
                </Label>
                <Input
                  id="slug"
                  {...register('slug')}
                  className="mt-2 font-mono"
                  placeholder="example-page"
                  dir="ltr"
                />
                {errors.slug && (
                  <p className="text-sm text-red-500 mt-1">{errors.slug.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="content">محتوى الصفحة</Label>
                <Textarea
                  id="content"
                  {...register('content')}
                  rows={10}
                  className="mt-2"
                  placeholder="أدخل محتوى الصفحة..."
                  dir="rtl"
                />
                {errors.content && (
                  <p className="text-sm text-red-500 mt-1">{errors.content.message}</p>
                )}
              </div>
            </div>
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsAddModalOpen(false)}>
                إلغاء
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="ml-2 h-4 w-4 animate-spin" />}
                إضافة الصفحة
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteAlertOpen} onOpenChange={setIsDeleteAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد من حذف هذه الصفحة؟</AlertDialogTitle>
            <AlertDialogDescription>
              لا يمكن التراجع عن هذا الإجراء بعد تنفيذه.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete}>تأكيد الحذف</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

function getContentTypeDescription(type: string): string {
  const types = {
    text: 'محتوى نصي',
    list: 'قائمة عناصر',
    packages: 'باقات وعروض',
    contact: 'معلومات اتصال',
    map: 'خريطة الموقع',
    schedule: 'جدول المواعيد'
  };
  return types[type as keyof typeof types] || 'محتوى عام';
}

    