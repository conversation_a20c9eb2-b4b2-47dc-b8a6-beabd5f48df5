"use client";

import { useState, useEffect } from 'react';
import { BookingsTable } from '@/components/bookings/bookings-table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';

interface Booking {
  name: string;
  phone: string;
  nationality: string;
  birthDate: string;
  email: string;
  packageTitle: string;
  packageType: 'Hajj' | 'Umrah';
  submittedAt: string;
  status?: 'pending' | 'contacted' | 'confirmed' | 'cancelled';
}

export default function BookingsPage() {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadBookings();
  }, []);

  const loadBookings = async () => {
    try {
      const response = await fetch('/api/bookings');
      if (!response.ok) throw new Error('Failed to load bookings');
      const data = await response.json();
      setBookings(data.map((booking: Booking) => ({
        ...booking,
        status: booking.status || 'pending'
      })));
    } catch (error) {
      toast({
        title: "خطأ في تحميل الطلبات",
        description: "حدث خطأ أثناء تحميل الطلبات. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateStatus = async (booking: Booking, newStatus: Booking['status']) => {
    try {
      const response = await fetch('/api/bookings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...booking,
          status: newStatus,
        }),
      });

      if (!response.ok) throw new Error('Failed to update booking status');

      setBookings(bookings.map(b => 
        b.submittedAt === booking.submittedAt ? { ...b, status: newStatus } : b
      ));

      toast({
        title: "تم تحديث الحالة",
        description: "تم تحديث حالة الطلب بنجاح.",
      });
    } catch (error) {
      toast({
        title: "خطأ في تحديث الحالة",
        description: "حدث خطأ أثناء تحديث حالة الطلب. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    }
  };

  const hajjBookings = bookings.filter(b => b.packageType === 'Hajj');
  const umrahBookings = bookings.filter(b => b.packageType === 'Umrah');

  if (isLoading) {
    return <div className="flex items-center justify-center min-h-screen">جاري التحميل...</div>;
  }

  return (
    <div className="container mx-auto py-10">
      <Card>
        <CardHeader>
          <CardTitle>إدارة طلبات الحج والعمرة</CardTitle>
          <CardDescription>
            عرض وإدارة جميع طلبات الحج والعمرة المقدمة من خلال الموقع
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" className="space-y-4">
            <TabsList>
              <TabsTrigger value="all">جميع الطلبات</TabsTrigger>
              <TabsTrigger value="hajj">طلبات الحج</TabsTrigger>
              <TabsTrigger value="umrah">طلبات العمرة</TabsTrigger>
            </TabsList>
            <TabsContent value="all">
              <BookingsTable bookings={bookings} onUpdateStatus={handleUpdateStatus} />
            </TabsContent>
            <TabsContent value="hajj">
              <BookingsTable bookings={hajjBookings} onUpdateStatus={handleUpdateStatus} />
            </TabsContent>
            <TabsContent value="umrah">
              <BookingsTable bookings={umrahBookings} onUpdateStatus={handleUpdateStatus} />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
} 