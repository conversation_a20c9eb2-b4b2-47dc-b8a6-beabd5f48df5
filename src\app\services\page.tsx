import Image from 'next/image';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Briefcase } from 'lucide-react';
import { KaabaIcon } from '@/components/icons';
import { PackageCard } from '@/components/package-card';
import PageHeader from '@/components/layout/PageHeader';
import type { Package } from '@/app/admin/pages/components/PackageEditor';
import type { AdditionalService } from '@/app/admin/pages/components/AdditionalServicesEditor';

async function getServiceContent() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/services`, { 
      next: { revalidate: 0 },
      cache: 'no-store'
    });
    if (!response.ok) {
      throw new Error('Failed to fetch service content');
    }
    return await response.json();
  } catch (error) {
    console.error('Error loading service content:', error);
    // Return default content if API fails
    return {
      hajjPackages: [],
      umrahPackages: [],
      additionalServices: []
    };
  }
}

export default async function ServicesPage() {
  const { hajjPackages, umrahPackages, additionalServices, pageHeader } = await getServiceContent();

  return (
    <div className="space-y-20 md:space-y-28 fade-in-up">
      <PageHeader
        title="خدماتنا"
        subtitle="نقدم في إيثار مجموعة متنوعة من باقات الحج والعمرة المصممة بعناية لتلبية كافة احتياجاتكم وتطلعاتكم، مع التزامنا بتقديم أعلى مستويات الجودة والراحة لضمان رحلة إيمانية لا تُنسى."
        backgroundImage={pageHeader?.backgroundImage}
      />

      {/* Hajj Packages */}
      <section id="hajj-packages">
        <div className="flex items-center justify-center mb-12">
            <KaabaIcon />
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mr-4 rtl:ml-4">باقات الحج</h2>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-10">
          {hajjPackages.map((pkg: Package, index: number) => (
            <div key={pkg.id} style={{animationDelay: `${index * 100}ms`}}>
               <PackageCard {...pkg} type="Hajj" />
            </div>
          ))}
        </div>
      </section>

      {/* Umrah Packages */}
      <section id="umrah-packages">
        <div className="flex items-center justify-center mb-12">
            <Briefcase className="h-10 w-10 text-primary" />
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mr-4 rtl:ml-4">باقات العمرة</h2>
        </div>
        <div className="grid md:grid-cols-2 gap-10">
          {umrahPackages.map((pkg: Package, index: number) => (
             <div key={pkg.id} style={{animationDelay: `${index * 100}ms`}}>
                <PackageCard {...pkg} type="Umrah" />
             </div>
          ))}
        </div>
      </section>

      <section className="py-16 bg-gradient-to-r from-primary/5 via-background to-accent/5 rounded-xl shadow-sm">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-foreground mb-12 fade-in-up">خدماتنا الإضافية التي نقدمها</h2>
          <p className="text-lg text-muted-foreground mb-12 max-w-2xl mx-auto text-center leading-relaxed fade-in-up" style={{animationDelay: '100ms'}}>
            بالإضافة إلى باقاتنا الأساسية، نقدم مجموعة من الخدمات الإضافية المصممة لتجعل رحلتكم أكثر راحة وسهولة ويسر.
          </p>
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {additionalServices.map((service: AdditionalService, index: number) => {
              const IconComponent = service.icon as any;
              return (
                <Card key={service.id} className="p-6 bg-card shadow-lg hover:shadow-xl transition-shadow duration-300 h-full flex flex-col fade-in-up" style={{animationDelay: `${(index * 100) + 200}ms`}}>
                  <CardHeader className="items-center text-center">
                    <div className="p-3 bg-primary/10 rounded-full w-fit mb-3">
                        <IconComponent className="h-8 w-8 text-primary" />
                    </div>
                    <CardTitle className="text-xl text-primary">{service.name}</CardTitle>
                  </CardHeader>
                  <CardContent className="flex-grow text-center">
                    <p className="text-sm text-muted-foreground leading-relaxed">{service.description}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
           <div className="text-center mt-16 fade-in-up" style={{animationDelay: '500ms'}}>
            <Link href="/contact" passHref>
                <Button size="lg">اطلب خدمتك الآن</Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
