import type { Package } from '@/app/admin/pages/components/PackageEditor';
import type { AdditionalService } from '@/app/admin/pages/components/AdditionalServicesEditor';

export const initialHajjPackages: Package[] = [
  {
    id: '1',
    title: 'باقة الحج الاقتصادية',
    description: 'باقة اقتصادية مناسبة تشمل جميع الخدمات الأساسية لرحلة حج مريحة وميسرة.',
    price: '15,000 ريال',
    duration: '15 يوم',
    imageUrl: 'https://placehold.co/600x400/e2e8f0/1e293b?text=باقة+الحج+الاقتصادية',
    features: [
      'سكن في فنادق 3 نجوم',
      'وجبات يومية حلال',
      'مواصلات بين المشاعر المقدسة',
      'مرشد ديني متخصص',
      'تأمين طبي أساسي'
    ]
  },
  {
    id: '2',
    title: 'باقة الحج المميزة',
    description: 'باقة متكاملة تجمع بين الراحة والقيمة المناسبة مع خدمات إضافية مميزة.',
    price: '25,000 ريال',
    duration: '15 يوم',
    imageUrl: 'https://placehold.co/600x400/e2e8f0/1e293b?text=باقة+الحج+المميزة',
    features: [
      'سكن في فنادق 4 نجوم',
      'ثلاث وجبات يومية فاخرة',
      'مواصلات VIP',
      'مرشد ديني وإداري',
      'تأمين طبي شامل',
      'هدايا تذكارية'
    ]
  },
  {
    id: '3',
    title: 'باقة الحج الفاخرة',
    description: 'أرقى مستويات الخدمة لضيوف الرحمن مع عناية فائقة واهتمام بأدق التفاصيل.',
    price: '35,000 ريال',
    duration: '16 يوم',
    imageUrl: 'https://placehold.co/600x400/e2e8f0/1e293b?text=باقة+الحج+الفاخرة',
    features: [
      'سكن في فنادق 5 نجوم',
      'وجبات فاخرة مع خيارات متعددة',
      'مواصلات VIP مع سائق خاص',
      'فريق خدمة متكامل',
      'تأمين طبي شامل مع خدمة الطوارئ',
      'جلسات توعوية وتثقيفية',
      'هدايا فاخرة'
    ]
  }
];

export const initialUmrahPackages: Package[] = [
  {
    id: '1',
    title: 'باقة العمرة السريعة',
    description: 'باقة مثالية لمن يرغب في أداء العمرة في رحلة قصيرة وميسرة.',
    price: '2,000 ريال',
    duration: '3 أيام',
    imageUrl: 'https://placehold.co/600x400/e2e8f0/1e293b?text=باقة+العمرة+السريعة',
    features: [
      'سكن في فندق قريب من الحرم',
      'وجبتين يومياً',
      'مواصلات من وإلى المطار',
      'مرشد ديني'
    ]
  },
  {
    id: '2',
    title: 'باقة العمرة الشاملة',
    description: 'باقة متكاملة تشمل زيارة المدينة المنورة مع خدمات مميزة.',
    price: '4,000 ريال',
    duration: '7 أيام',
    imageUrl: 'https://placehold.co/600x400/e2e8f0/1e293b?text=باقة+العمرة+الشاملة',
    features: [
      'سكن في فنادق 4 نجوم في مكة والمدينة',
      'ثلاث وجبات يومية',
      'مواصلات بين المدن والمطار',
      'زيارة المعالم التاريخية',
      'مرشد متخصص طوال الرحلة'
    ]
  }
];

export const initialAdditionalServices: AdditionalService[] = [
  {
    id: '1',
    name: 'إصدار التأشيرات',
    description: 'نساعدك في استخراج تأشيرات الحج والعمرة بسهولة ويسر.',
    icon: 'FileText'
  },
  {
    id: '2',
    name: 'حجز طيران',
    description: 'نوفر حجوزات طيران بأفضل الأسعار وعلى مختلف الخطوط الجوية.',
    icon: 'Plane'
  },
  {
    id: '3',
    name: 'حجوزات فندقية',
    description: 'مجموعة واسعة من الفنادق في مكة والمدينة تناسب جميع الميزانيات.',
    icon: 'Hotel'
  },
  {
    id: '4',
    name: 'مواصلات خاصة',
    description: 'خدمات نقل خاصة ومريحة من وإلى المطار وبين المشاعر المقدسة.',
    icon: 'Car'
  },
  {
    id: '5',
    name: 'وجبات إضافية',
    description: 'إمكانية طلب وجبات إضافية متنوعة تناسب جميع الأذواق.',
    icon: 'Utensils'
  },
  {
    id: '6',
    name: 'مرشدون متخصصون',
    description: 'مرشدون دينيون وإداريون لمرافقتكم وتقديم الدعم اللازم.',
    icon: 'Users'
  },
  {
    id: '7',
    name: 'برامج سياحية إسلامية',
    description: 'زيارات إيمانية وثقافية لأهم المعالم الإسلامية في مكة والمدينة.',
    icon: 'BookOpen'
  },
  {
    id: '8',
    name: 'خدمات كبار الشخصيات (VIP)',
    description: 'خدمات استقبال ومرافقة خاصة وتلبية كافة احتياجات كبار الشخصيات.',
    icon: 'Crown'
  },
  {
    id: '9',
    name: 'تأمين سفر',
    description: 'نوفر خدمة تأمين السفر لتغطية أي طوارئ خلال رحلتكم.',
    icon: 'ShieldCheck'
  }
]; 