'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { FileEdit, PlusCircle, Trash2, Save, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useForm, useFieldArray, type SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import Image from 'next/image';
import { updateHomepage } from '@/app/actions/homepage';
import { FileUpload } from '@/components/admin/FileUpload';

// Schemas for the form
const heroContentSchema = z.object({
  backgroundImageUrl: z.string().min(1, {message: "صورة الخلفية مطلوبة"}),
  backgroundOpacity: z.number().min(0).max(1),
  titleColor: z.string().min(1, {message: "لون العنوان الرئيسي مطلوب"}),
  subtitleColor: z.string().min(1, {message: "لون العنوان الفرعي مطلوب"}),
  dataAiHint: z.string().min(1, { message: "الرجاء إدخال تلميح AI للخلفية" }),
  title: z.string().min(5, { message: "العنوان الرئيسي يجب أن يكون 5 أحرف على الأقل" }),
  subtitle: z.string().min(10, { message: "العنوان الفرعي يجب أن يكون 10 أحرف على الأقل" }),
});

const tripItemSchema = z.object({
  id: z.number(),
  imageUrl: z.string().min(1, {message: "حقل صورة الرحلة مطلوب"}),
  dataAiHint: z.string().min(1, { message: "الرجاء إدخال تلميح AI لصورة الرحلة" }),
  title: z.string().min(3, { message: "عنوان الرحلة يجب أن يكون 3 أحرف على الأقل" }),
  description: z.string().min(5, { message: "وصف الرحلة يجب أن يكون 5 أحرف على الأقل" }),
});

const homepageEditorSchema = z.object({
  hero: heroContentSchema,
  latestTrips: z.array(tripItemSchema).min(1, {message: "يجب أن يكون هناك رحلة واحدة على الأقل"}),
});

type HomepageEditorFormData = z.infer<typeof homepageEditorSchema>;

interface HomepageEditorProps {
  initialContent: HomepageEditorFormData;
}

export function HomepageEditor({ initialContent }: HomepageEditorProps) {
  const { toast } = useToast();
  const { control, register, handleSubmit, getValues, setValue, formState: { errors, isSubmitting } } = useForm<HomepageEditorFormData>({
    resolver: zodResolver(homepageEditorSchema),
    defaultValues: {
      ...initialContent,
      hero: {
        ...initialContent.hero,
        backgroundOpacity: initialContent.hero?.backgroundOpacity ?? 0.5,
        titleColor: initialContent.hero?.titleColor ?? '#FFFFFF',
        subtitleColor: initialContent.hero?.subtitleColor ?? '#FFFFFF'
      }
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "latestTrips",
  });

  const onSubmit: SubmitHandler<HomepageEditorFormData> = async (data) => {
    try {
      const result = await updateHomepage({
        title: data.hero.title,
        subtitle: data.hero.subtitle,
        backgroundImageUrl: data.hero.backgroundImageUrl,
        backgroundOpacity: data.hero.backgroundOpacity,
        titleColor: data.hero.titleColor,
        subtitleColor: data.hero.subtitleColor,
        latestTrips: data.latestTrips.map(trip => ({
          id: trip.id,
          imageUrl: trip.imageUrl,
          dataAiHint: trip.dataAiHint,
          title: trip.title,
          description: trip.description
        }))
      });

      if (result.success) {
        toast({
          title: "تم حفظ التغييرات بنجاح",
          description: "تم تحديث محتوى الصفحة الرئيسية والرحلات السابقة.",
        });
      } else {
        toast({
          title: "حدث خطأ",
          description: result.error || "فشل تحديث محتوى الصفحة الرئيسية",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error updating homepage:', error);
      toast({
        title: "حدث خطأ",
        description: "فشل تحديث محتوى الصفحة الرئيسية",
        variant: "destructive",
      });
    }
  };

  const addNewTrip = () => {
    append({
      id: Date.now(),
      imageUrl: 'https://placehold.co/600x400.png?text=رحلة+جديدة',
      dataAiHint: 'new trip',
      title: 'عنوان رحلة جديدة',
      description: 'وصف قصير للرحلة الجديدة.',
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <FileEdit className="h-6 w-6 text-primary" />
            <CardTitle>إدارة محتوى الصفحة الرئيسية</CardTitle>
          </div>
          <CardDescription>
            قم بتعديل النصوص والصور المعروضة في الصفحة الرئيسية لموقعك.
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit(onSubmit)}>
          <CardContent className="space-y-8">
            {/* Hero Section Editor */}
            <Card className="bg-muted/30">
              <CardHeader>
                <CardTitle className="text-xl">قسم Hero (العلوي)</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="hero-background-image">صورة الخلفية (المقاس المثالي: 1200×600)</Label>
                    <div className="flex items-center gap-4 mt-1">
                      <Input
                        id="hero-background-image"
                        type="file"
                        accept="image/*"
                        onChange={async (e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            const formData = new FormData();
                            formData.append('file', file);
                            formData.append('title', 'خلفية الصفحة الرئيسية');
                            formData.append('type', 'IMAGE');
                            
                            try {
                              const response = await fetch('/api/media/upload', {
                                method: 'POST',
                                body: formData,
                              });
                              
                              const data = await response.json();
                              if (data.url) {
                                const url = `/uploads/${data.url.split('/').pop()}`;
                                setValue('hero.backgroundImageUrl', url, { shouldValidate: true });
                              }
                            } catch (error) {
                              console.error('Error uploading file:', error);
                              toast({
                                title: "خطأ في رفع الصورة",
                                description: "حدث خطأ أثناء رفع الصورة، الرجاء المحاولة مرة أخرى",
                                variant: "destructive"
                              });
                            }
                          }
                        }}
                      />
                    </div>
                    {errors.hero?.backgroundImageUrl && (
                      <p className="text-destructive text-sm mt-1">{errors.hero.backgroundImageUrl.message}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label htmlFor="hero.backgroundOpacity">درجة وضوح الصورة</Label>
                    <div className="flex items-center gap-4">
                      <input
                        type="range"
                        min="0"
                        max="100"
                        step="5"
                        className="w-full"
                        value={getValues('hero.backgroundOpacity') * 100}
                        onChange={(e) => {
                          setValue('hero.backgroundOpacity', Number(e.target.value) / 100, { shouldValidate: true });
                        }}
                      />
                      <span className="text-sm text-muted-foreground w-16 text-center">
                        {Math.round((getValues('hero.backgroundOpacity') || 0.5) * 100)}%
                      </span>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="hero.titleColor">لون العنوان الرئيسي</Label>
                    <div className="flex items-center gap-4">
                      <Input
                        type="color"
                        id="hero.titleColor"
                        {...register('hero.titleColor')}
                        className="w-20 h-10"
                      />
                      <span className="text-sm text-muted-foreground">
                        {getValues('hero.titleColor') || '#FFFFFF'}
                      </span>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="hero.subtitleColor">لون العنوان الفرعي</Label>
                    <div className="flex items-center gap-4">
                      <Input
                        type="color"
                        id="hero.subtitleColor"
                        {...register('hero.subtitleColor')}
                        className="w-20 h-10"
                      />
                      <span className="text-sm text-muted-foreground">
                        {getValues('hero.subtitleColor') || '#FFFFFF'}
                      </span>
                    </div>
                  </div>

                  {getValues('hero.backgroundImageUrl') && (
                    <div className="mt-4">
                      <p className="text-sm text-muted-foreground mb-2">المعاينة:</p>
                      <div className="relative w-full h-40 bg-muted rounded-lg overflow-hidden">
                        <Image
                          src={getValues('hero.backgroundImageUrl')}
                          alt="معاينة خلفية القسم العلوي"
                          fill
                          className="object-cover"
                          style={{
                            opacity: getValues('hero.backgroundOpacity')
                          }}
                        />
                      </div>
                    </div>
                  )}
                </div>
                <div>
                  <Label htmlFor="hero.dataAiHint">تلميح AI لصورة الخلفية</Label>
                  <Input id="hero.dataAiHint" {...register("hero.dataAiHint")} className={`mt-1 ${errors.hero?.dataAiHint ? 'border-destructive' : ''}`} />
                  {errors.hero?.dataAiHint && <p className="text-destructive text-sm mt-1">{errors.hero.dataAiHint.message}</p>}
                </div>
                <div>
                  <Label htmlFor="hero.title">العنوان الرئيسي</Label>
                  <Input id="hero.title" {...register("hero.title")} className={`mt-1 ${errors.hero?.title ? 'border-destructive' : ''}`} />
                  {errors.hero?.title && <p className="text-destructive text-sm mt-1">{errors.hero.title.message}</p>}
                </div>
                <div>
                  <Label htmlFor="hero.subtitle">العنوان الفرعي (الوصف)</Label>
                  <Textarea id="hero.subtitle" {...register("hero.subtitle")} rows={3} className={`mt-1 ${errors.hero?.subtitle ? 'border-destructive' : ''}`} />
                  {errors.hero?.subtitle && <p className="text-destructive text-sm mt-1">{errors.hero.subtitle.message}</p>}
                </div>
              </CardContent>
            </Card>

            {/* Latest Trips Editor */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-xl">قسم "من رحلاتنا السابقة"</CardTitle>
                <Button type="button" variant="outline" onClick={addNewTrip}>
                  <PlusCircle className="ml-2 h-4 w-4" /> إضافة رحلة
                </Button>
              </CardHeader>
              <CardContent className="space-y-6">
                {fields.map((field, index) => (
                  <Card key={field.id} className="p-4 border shadow-sm">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-semibold">رحلة #{index + 1}</h4>
                      <Button type="button" variant="ghost" size="icon" onClick={() => remove(index)} title="حذف الرحلة">
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <Label>صورة الرحلة</Label>
                        <FileUpload
                          onUploadComplete={(url) => {
                            setValue(`latestTrips.${index}.imageUrl`, url);
                          }}
                          category="PREVIOUS_TRIP"
                          className="mt-1"
                        />
                        {errors.latestTrips?.[index]?.imageUrl && (
                          <p className="text-destructive text-sm mt-1">{errors.latestTrips?.[index]?.imageUrl?.message}</p>
                        )}
                      </div>
                      <div>
                        <Label htmlFor={`latestTrips.${index}.dataAiHint`}>تلميح AI لصورة الرحلة</Label>
                        <Input
                          id={`latestTrips.${index}.dataAiHint`}
                          {...register(`latestTrips.${index}.dataAiHint`)}
                          className={`mt-1 ${errors.latestTrips?.[index]?.dataAiHint ? 'border-destructive' : ''}`}
                        />
                        {errors.latestTrips?.[index]?.dataAiHint && (
                          <p className="text-destructive text-sm mt-1">{errors.latestTrips?.[index]?.dataAiHint?.message}</p>
                        )}
                      </div>
                      {/* Display image preview */}
                      {getValues(`latestTrips.${index}.imageUrl`) && (
                        <div className="flex items-center justify-center">
                          <Image
                            src={getValues(`latestTrips.${index}.imageUrl`)}
                            alt={`Preview for ${getValues(`latestTrips.${index}.title`)}`}
                            width={150}
                            height={100}
                            className="object-cover rounded-md border"
                            onError={(e) => (e.currentTarget.src = 'https://placehold.co/150x100.png?text=خطأ+في+الصورة')}
                            data-ai-hint={getValues(`latestTrips.${index}.dataAiHint`) || 'trip image'}
                          />
                        </div>
                      )}
                    </div>
                    <div className="mt-4">
                      <Label htmlFor={`latestTrips.${index}.title`}>عنوان الرحلة</Label>
                      <Input
                        id={`latestTrips.${index}.title`}
                        {...register(`latestTrips.${index}.title`)}
                        className={`mt-1 ${errors.latestTrips?.[index]?.title ? 'border-destructive' : ''}`}
                      />
                      {errors.latestTrips?.[index]?.title && (
                        <p className="text-destructive text-sm mt-1">{errors.latestTrips?.[index]?.title?.message}</p>
                      )}
                    </div>
                    <div className="mt-4">
                      <Label htmlFor={`latestTrips.${index}.description`}>وصف الرحلة</Label>
                      <Textarea
                        id={`latestTrips.${index}.description`}
                        {...register(`latestTrips.${index}.description`)}
                        rows={2}
                        className={`mt-1 ${errors.latestTrips?.[index]?.description ? 'border-destructive' : ''}`}
                      />
                      {errors.latestTrips?.[index]?.description && (
                        <p className="text-destructive text-sm mt-1">{errors.latestTrips?.[index]?.description?.message}</p>
                      )}
                    </div>
                  </Card>
                ))}
                {errors.latestTrips && !Array.isArray(errors.latestTrips) && (
                  <p className="text-destructive text-sm mt-1">{errors.latestTrips.message}</p>
                )}
              </CardContent>
            </Card>
          </CardContent>
          <CardFooter>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin rtl:mr-2" />
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <Save className="ml-2 h-4 w-4 rtl:mr-2" />
                  حفظ التغييرات
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
} 