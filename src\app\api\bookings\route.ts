import { NextResponse } from 'next/server';
import { writeFile, readFile, mkdir } from 'fs/promises';
import { join } from 'path';

interface BookingData {
  name: string;
  phone: string;
  nationality: string;
  birthDate: string;
  email: string;
  packageTitle: string;
  packageType: 'Hajj' | 'Umrah';
  submittedAt: string;
  status?: 'pending' | 'contacted' | 'confirmed' | 'cancelled';
}

const getBookingsFilePath = () => join(process.cwd(), 'public', 'data', 'bookings.json');

export async function POST(request: Request) {
  try {
    const booking: BookingData = await request.json();
    
    // Save to a JSON file in the data directory
    const dataPath = join(process.cwd(), 'public', 'data');
    const filePath = getBookingsFilePath();
    
    // Create data directory if it doesn't exist
    await mkdir(dataPath, { recursive: true });
    
    // Read existing bookings
    let bookings: BookingData[] = [];
    try {
      const fileContent = await readFile(filePath, 'utf-8');
      bookings = JSON.parse(fileContent);
    } catch (error) {
      // File doesn't exist or is invalid, start with empty array
      console.log('No existing bookings file found, creating new one');
    }
    
    // Add new booking with default status
    bookings.push({
      ...booking,
      status: 'pending'
    });
    
    // Save updated bookings
    await writeFile(filePath, JSON.stringify(bookings, null, 2));

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error saving booking:', error);
    return NextResponse.json(
      { error: 'Error saving booking' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const filePath = getBookingsFilePath();
    
    try {
      const fileContent = await readFile(filePath, 'utf-8');
      const bookings = JSON.parse(fileContent);
      return NextResponse.json(bookings);
    } catch (error) {
      // File doesn't exist or is invalid, return empty array
      return NextResponse.json([]);
    }
  } catch (error) {
    console.error('Error reading bookings:', error);
    return NextResponse.json({ error: 'Error reading bookings' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const updatedBooking: BookingData = await request.json();
    const filePath = getBookingsFilePath();
    
    // Read existing bookings
    let bookings: BookingData[] = [];
    try {
      const fileContent = await readFile(filePath, 'utf-8');
      bookings = JSON.parse(fileContent);
    } catch (error) {
      throw new Error('Failed to read bookings file');
    }
    
    // Update booking status
    const updatedBookings = bookings.map(booking => 
      booking.submittedAt === updatedBooking.submittedAt
        ? { ...booking, status: updatedBooking.status }
        : booking
    );
    
    // Save updated bookings
    await writeFile(filePath, JSON.stringify(updatedBookings, null, 2));

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating booking:', error);
    return NextResponse.json(
      { error: 'Error updating booking' },
      { status: 500 }
    );
  }
} 