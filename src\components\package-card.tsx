"use client";

import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle } from 'lucide-react';
import type { Package } from '@/app/admin/pages/components/PackageEditor';
import { BookingForm } from '@/components/booking-form';
import { useState } from 'react';

interface PackageCardProps extends Package {
  type: 'Hajj' | 'Umrah';
}

export function PackageCard({ title, type, price, duration, features, imageUrl }: PackageCardProps) {
  const [showBookingForm, setShowBookingForm] = useState(false);

  return (
    <>
      <Card className="flex flex-col overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 h-full fade-in-up">
        <div className="relative w-full h-56">
          <Image 
            src={imageUrl || 'https://placehold.co/600x400/e2e8f0/1e293b?text=صورة+الباقة'} 
            alt={title}
            fill
            className="object-cover"
          />
          <div className={`absolute top-2 right-2 px-3 py-1 rounded-full text-sm font-semibold ${type === 'Hajj' ? 'bg-green-500 text-white' : 'bg-blue-500 text-white'}`}>
            {type === 'Hajj' ? 'باقة حج' : 'باقة عمرة'}
          </div>
        </div>
        <CardHeader>
          <CardTitle className="text-xl">{title}</CardTitle>
          <CardDescription className="text-lg font-semibold text-primary">{price}</CardDescription>
          <p className="text-sm text-muted-foreground">{duration}</p>
        </CardHeader>
        <CardContent className="flex-grow space-y-2">
          {features.map((feature, index) => (
            <div key={index} className="flex items-center">
              <CheckCircle className="h-5 w-5 text-green-500 ml-2 rtl:mr-2 flex-shrink-0" />
              <span className="text-sm">{feature}</span>
            </div>
          ))}
        </CardContent>
        <CardFooter>
          <Button className="w-full" onClick={() => setShowBookingForm(true)}>
            احجز الآن
          </Button>
        </CardFooter>
      </Card>

      <BookingForm
        isOpen={showBookingForm}
        onClose={() => setShowBookingForm(false)}
        packageTitle={title}
        packageType={type}
      />
    </>
  );
} 