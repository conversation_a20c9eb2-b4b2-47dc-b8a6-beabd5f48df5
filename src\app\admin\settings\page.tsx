'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Settings, Save, Loader2, UploadCloud, Mail } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useForm, type SubmitHandler, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useEffect, useState } from 'react';
import Image from 'next/image';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const settingsSchema = z.object({
  siteName: z.string().min(2, "اسم الموقع يجب ألا يقل عن حرفين."),
  siteDescription: z.string().min(10, "وصف الموقع يجب ألا يقل عن 10 أحرف."),
  siteLogo: z.string().optional(),
  smtpHost: z.string().min(1, "يجب إدخال عنوان خادم SMTP"),
  smtpPort: z.string().regex(/^\d+$/, "يجب إدخال رقم المنفذ بشكل صحيح"),
  smtpUser: z.string().min(1, "يجب إدخال اسم المستخدم"),
  smtpPassword: z.string().min(1, "يجب إدخال كلمة المرور"),
  smtpFromEmail: z.string().email("البريد الإلكتروني غير صحيح"),
  smtpFromName: z.string().min(1, "يجب إدخال اسم المرسل"),
  contactEmail: z.string().email("البريد الإلكتروني للتواصل غير صحيح."),
  contactPhone: z.string().min(9, "رقم الهاتف يجب ألا يقل عن 9 أرقام.").regex(/^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$/, "صيغة رقم الهاتف غير صحيحة."),
  whatsappNumber: z.string().min(9, "رقم الواتساب يجب ألا يقل عن 9 أرقام.").regex(/^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$/, "صيغة رقم الواتساب غير صحيحة."),
  address: z.string().min(10, "العنوان يجب ألا يقل عن 10 أحرف."),
  workingHours: z.string().min(5, "ساعات العمل يجب ألا تقل عن 5 أحرف."),
  facebook: z.string().url("رابط فيسبوك غير صحيح").optional().or(z.literal('')),
  twitter: z.string().url("رابط تويتر غير صحيح").optional().or(z.literal('')),
  instagram: z.string().url("رابط انستجرام غير صحيح").optional().or(z.literal('')),
  youtube: z.string().url("رابط يوتيوب غير صحيح").optional().or(z.literal('')),
  website: z.string().url("رابط الموقع غير صحيح").optional().or(z.literal('')),
  mapLocation: z.object({
    lat: z.number(),
    lng: z.number(),
    zoom: z.number()
  }).optional()
});

type SettingsFormData = z.infer<typeof settingsSchema>;

export default function AdminSettingsPage() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);

  const { register, handleSubmit, reset, setValue, watch, formState: { errors, isSubmitting } } = useForm<SettingsFormData>({
    resolver: zodResolver(settingsSchema)
  });

  useEffect(() => {
    const loadSettings = async () => {
      try {
        const response = await fetch('/api/settings');
        if (!response.ok) throw new Error('Failed to load settings');
        const data = await response.json();
        reset({
          siteName: data.siteName || '',
          siteDescription: data.siteDescription || '',
          siteLogo: data.siteLogo || '',
          smtpHost: data.smtpHost || '',
          smtpPort: data.smtpPort || '',
          smtpUser: data.smtpUser || '',
          smtpPassword: data.smtpPassword || '',
          smtpFromEmail: data.smtpFromEmail || '',
          smtpFromName: data.smtpFromName || '',
          contactEmail: data.email || '',
          contactPhone: data.phone || '',
          whatsappNumber: data.whatsapp || '',
          address: data.address || '',
          workingHours: data.workingHours || '',
          facebook: data.facebook || '',
          twitter: data.twitter || '',
          instagram: data.instagram || '',
          youtube: data.youtube || '',
          website: data.website || '',
          mapLocation: data.mapLocation || undefined
        });
        if (data.siteLogo) {
          setLogoPreview(data.siteLogo);
        }
      } catch (error) {
        console.error('Error loading settings:', error);
        toast({
          title: "خطأ في تحميل الإعدادات",
          description: "حدث خطأ أثناء تحميل الإعدادات. يرجى تحديث الصفحة.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, [reset, toast]);

  const onSubmit: SubmitHandler<SettingsFormData> = async (data) => {
    try {
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          siteName: data.siteName,
          siteDescription: data.siteDescription,
          siteLogo: data.siteLogo,
          smtpHost: data.smtpHost,
          smtpPort: data.smtpPort,
          smtpUser: data.smtpUser,
          smtpPassword: data.smtpPassword,
          smtpFromEmail: data.smtpFromEmail,
          smtpFromName: data.smtpFromName,
          email: data.contactEmail,
          phone: data.contactPhone,
          whatsapp: data.whatsappNumber,
          address: data.address,
          workingHours: data.workingHours,
          facebook: data.facebook,
          twitter: data.twitter,
          instagram: data.instagram,
          youtube: data.youtube,
          website: data.website,
          mapLocation: data.mapLocation
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || 'Failed to save settings');
      }

      toast({
        title: "تم حفظ الإعدادات",
        description: "تم تحديث إعدادات الموقع بنجاح.",
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: "خطأ في حفظ الإعدادات",
        description: error instanceof Error ? error.message : "حدث خطأ أثناء حفظ الإعدادات. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    }
  };

  const handleLogoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const formData = new FormData();
      formData.append('file', file);
      try {
        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });
        if (!response.ok) throw new Error('Failed to upload file');
        const data = await response.json();
        setValue('siteLogo', data.url);
        setLogoPreview(data.url);
      } catch (error) {
        console.error('Error uploading file:', error);
        toast({
          title: "خطأ في رفع الملف",
          description: "حدث خطأ أثناء رفع الشعار. يرجى المحاولة مرة أخرى.",
          variant: "destructive",
        });
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Settings className="h-6 w-6 text-primary" />
            <CardTitle>إعدادات الموقع</CardTitle>
          </div>
          <CardDescription>قم بتكوين إعدادات موقعك من هنا.</CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit(onSubmit)}>
          <CardContent className="space-y-8">
            {/* General Settings */}
            <section>
              <h3 className="text-xl font-semibold mb-4 text-primary border-b pb-2">الإعدادات العامة</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="siteName">اسم الموقع</Label>
                  <Input
                    id="siteName"
                    {...register("siteName")}
                    className={errors.siteName ? 'border-destructive' : ''}
                    placeholder="اسم موقعك"
                  />
                  {errors.siteName && (
                    <p className="text-sm text-destructive">{errors.siteName.message}</p>
                  )}
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="siteDescription">وصف الموقع</Label>
                  <Textarea
                    id="siteDescription"
                    {...register("siteDescription")}
                    className={errors.siteDescription ? 'border-destructive' : ''}
                    placeholder="وصف مختصر لموقعك"
                    rows={3}
                  />
                  {errors.siteDescription && (
                    <p className="text-sm text-destructive">{errors.siteDescription.message}</p>
                  )}
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="siteLogo">شعار الموقع</Label>
                  <div className="flex items-center gap-4">
                    <Input
                      id="siteLogo"
                      type="file"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      className={errors.siteLogo ? 'border-destructive' : ''}
                    />
                    {logoPreview && (
                      <div className="relative w-16 h-16">
                        <Image
                          src={logoPreview}
                          alt="شعار الموقع"
                          fill
                          className="object-contain"
                        />
                      </div>
                    )}
                  </div>
                  {errors.siteLogo && (
                    <p className="text-sm text-destructive">{errors.siteLogo.message}</p>
                  )}
                </div>
              </div>
            </section>

            {/* Email Settings */}
            <section>
              <h3 className="text-xl font-semibold mb-4 text-primary border-b pb-2">إعدادات البريد الإلكتروني</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="smtpHost">خادم SMTP</Label>
                  <Input
                    id="smtpHost"
                    {...register("smtpHost")}
                    className={errors.smtpHost ? 'border-destructive' : ''}
                    placeholder="smtp.example.com"
                    dir="ltr"
                  />
                  {errors.smtpHost && (
                    <p className="text-sm text-destructive">{errors.smtpHost.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="smtpPort">منفذ SMTP</Label>
                  <Input
                    id="smtpPort"
                    {...register("smtpPort")}
                    className={errors.smtpPort ? 'border-destructive' : ''}
                    placeholder="587"
                    dir="ltr"
                  />
                  {errors.smtpPort && (
                    <p className="text-sm text-destructive">{errors.smtpPort.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="smtpUser">اسم المستخدم</Label>
                  <Input
                    id="smtpUser"
                    {...register("smtpUser")}
                    className={errors.smtpUser ? 'border-destructive' : ''}
                    placeholder="<EMAIL>"
                    dir="ltr"
                  />
                  {errors.smtpUser && (
                    <p className="text-sm text-destructive">{errors.smtpUser.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="smtpPassword">كلمة المرور</Label>
                  <Input
                    id="smtpPassword"
                    type="password"
                    {...register("smtpPassword")}
                    className={errors.smtpPassword ? 'border-destructive' : ''}
                    placeholder="••••••••"
                    dir="ltr"
                  />
                  {errors.smtpPassword && (
                    <p className="text-sm text-destructive">{errors.smtpPassword.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="smtpFromEmail">البريد الإلكتروني للمرسل</Label>
                  <Input
                    id="smtpFromEmail"
                    {...register("smtpFromEmail")}
                    className={errors.smtpFromEmail ? 'border-destructive' : ''}
                    placeholder="<EMAIL>"
                    dir="ltr"
                  />
                  {errors.smtpFromEmail && (
                    <p className="text-sm text-destructive">{errors.smtpFromEmail.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="smtpFromName">اسم المرسل</Label>
                  <Input
                    id="smtpFromName"
                    {...register("smtpFromName")}
                    className={errors.smtpFromName ? 'border-destructive' : ''}
                    placeholder="اسم الشركة"
                  />
                  {errors.smtpFromName && (
                    <p className="text-sm text-destructive">{errors.smtpFromName.message}</p>
                  )}
                </div>
              </div>
            </section>

            {/* Contact Information */}
            <section>
              <h3 className="text-xl font-semibold mb-4 text-primary border-b pb-2">معلومات الاتصال</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="contactEmail">البريد الإلكتروني للتواصل</Label>
                  <Input
                    id="contactEmail"
                    type="email"
                    {...register("contactEmail")}
                    className={errors.contactEmail ? 'border-destructive' : ''}
                    placeholder="<EMAIL>"
                  />
                  {errors.contactEmail && (
                    <p className="text-sm text-destructive">{errors.contactEmail.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contactPhone">رقم الهاتف للتواصل</Label>
                  <Input
                    id="contactPhone"
                    {...register("contactPhone")}
                    className={errors.contactPhone ? 'border-destructive' : ''}
                    dir="ltr"
                    placeholder="+966512345678"
                  />
                  {errors.contactPhone && (
                    <p className="text-sm text-destructive">{errors.contactPhone.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="whatsappNumber">رقم الواتساب</Label>
                  <Input
                    id="whatsappNumber"
                    {...register("whatsappNumber")}
                    className={errors.whatsappNumber ? 'border-destructive' : ''}
                    dir="ltr"
                    placeholder="+966512345678"
                  />
                  {errors.whatsappNumber && (
                    <p className="text-sm text-destructive">{errors.whatsappNumber.message}</p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    أدخل رقم الواتساب بالتنسيق الدولي (مثال: +966512345678)
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="workingHours">ساعات العمل</Label>
                  <Input
                    id="workingHours"
                    {...register("workingHours")}
                    className={errors.workingHours ? 'border-destructive' : ''}
                    placeholder="من السبت إلى الخميس: 9:00 صباحاً - 5:00 مساءً"
                  />
                  {errors.workingHours && (
                    <p className="text-sm text-destructive">{errors.workingHours.message}</p>
                  )}
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="address">العنوان</Label>
                  <Textarea
                    id="address"
                    {...register("address")}
                    className={errors.address ? 'border-destructive' : ''}
                    rows={3}
                    placeholder="أدخل العنوان الكامل"
                  />
                  {errors.address && (
                    <p className="text-sm text-destructive">{errors.address.message}</p>
                  )}
                </div>
              </div>
            </section>

            {/* Social Media Links */}
            <section>
              <h3 className="text-xl font-semibold mb-4 text-primary border-b pb-2">روابط التواصل الاجتماعي</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="facebook">رابط فيسبوك</Label>
                  <Input
                    id="facebook"
                    {...register("facebook")}
                    className={errors.facebook ? 'border-destructive' : ''}
                    placeholder="https://facebook.com/yourpage"
                    dir="ltr"
                  />
                  {errors.facebook && (
                    <p className="text-sm text-destructive">{errors.facebook.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="twitter">رابط تويتر</Label>
                  <Input
                    id="twitter"
                    {...register("twitter")}
                    className={errors.twitter ? 'border-destructive' : ''}
                    placeholder="https://twitter.com/yourprofile"
                    dir="ltr"
                  />
                  {errors.twitter && (
                    <p className="text-sm text-destructive">{errors.twitter.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="instagram">رابط انستجرام</Label>
                  <Input
                    id="instagram"
                    {...register("instagram")}
                    className={errors.instagram ? 'border-destructive' : ''}
                    placeholder="https://instagram.com/yourprofile"
                    dir="ltr"
                  />
                  {errors.instagram && (
                    <p className="text-sm text-destructive">{errors.instagram.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="youtube">رابط يوتيوب</Label>
                  <Input
                    id="youtube"
                    {...register("youtube")}
                    className={errors.youtube ? 'border-destructive' : ''}
                    placeholder="https://youtube.com/yourchannel"
                    dir="ltr"
                  />
                  {errors.youtube && (
                    <p className="text-sm text-destructive">{errors.youtube.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="website">رابط الموقع الإلكتروني</Label>
                  <Input
                    id="website"
                    {...register("website")}
                    className={errors.website ? 'border-destructive' : ''}
                    placeholder="https://yourwebsite.com"
                    dir="ltr"
                  />
                  {errors.website && (
                    <p className="text-sm text-destructive">{errors.website.message}</p>
                  )}
                </div>
              </div>
            </section>
          </CardContent>
          <CardFooter>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="ml-2 h-4 w-4 animate-spin rtl:mr-2" />}
              <Save className="ml-2 h-4 w-4 rtl:mr-2" />
              حفظ الإعدادات
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}

    
