'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Image as ImageIcon, Upload } from 'lucide-react';
import Image from 'next/image';
import { MediaCategory } from '@prisma/client';
import { FileUpload } from '@/components/admin/FileUpload';

interface Media {
  id: string;
  url: string;
  title: string;
  description?: string;
  category: MediaCategory;
}

interface PageBackgroundManagerProps {
  pageName: string;
  pageTitle: string;
  currentBackgroundUrl?: string;
}

export function PageBackgroundManager({ pageName, pageTitle, currentBackgroundUrl }: PageBackgroundManagerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isUploadMode, setIsUploadMode] = useState(false);
  const [images, setImages] = useState<Media[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | undefined>(currentBackgroundUrl);
  const { toast } = useToast();

  useEffect(() => {
    if (isOpen && !isUploadMode) {
      fetchImages();
    }
  }, [isOpen, isUploadMode]);

  const fetchImages = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/media?category=PAGE_BACKGROUND');
      if (response.ok) {
        const data = await response.json();
        setImages(data);
      }
    } catch (error) {
      console.error('Error fetching images:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء تحميل الصور',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleImageSelect = async (imageUrl: string) => {
    try {
      setLoading(true);
      const response = await fetch('/api/pages', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: pageName.toUpperCase(),
          title: pageName,
          content: JSON.stringify({ backgroundImage: imageUrl }),
        }),
      });

      if (response.ok) {
        setSelectedImage(imageUrl);
        toast({
          title: 'تم التحديث',
          description: 'تم تحديث خلفية الصفحة بنجاح',
        });
      } else {
        throw new Error('Failed to update page background');
      }
    } catch (error) {
      console.error('Error updating page background:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء تحديث خلفية الصفحة',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
      setIsOpen(false);
    }
  };

  const handleUploadComplete = async (uploadedFile: { url: string }) => {
    await handleImageSelect(uploadedFile.url);
    setIsUploadMode(false);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>خلفية صفحة {pageTitle}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {selectedImage ? (
            <div className="relative aspect-video rounded-lg overflow-hidden">
              <Image
                src={selectedImage}
                alt={`خلفية صفحة ${pageTitle}`}
                fill
                className="object-cover"
              />
            </div>
          ) : (
            <div className="aspect-video rounded-lg bg-muted flex items-center justify-center">
              <ImageIcon className="h-12 w-12 text-muted-foreground" />
            </div>
          )}
          <div className="flex gap-2">
            <Button onClick={() => { setIsOpen(true); setIsUploadMode(false); }} className="flex-1">
              اختيار من المكتبة
            </Button>
            <Button onClick={() => { setIsOpen(true); setIsUploadMode(true); }} className="flex-1">
              <Upload className="h-4 w-4 ml-2" />
              رفع صورة جديدة
            </Button>
          </div>
        </div>

        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>
                {isUploadMode ? 'رفع صورة جديدة' : 'اختر خلفية الصفحة'}
              </DialogTitle>
            </DialogHeader>
            {isUploadMode ? (
              <FileUpload
                category="PAGE_BACKGROUND"
                onUploadComplete={handleUploadComplete}
                maxFiles={1}
                acceptedFileTypes={{ 'image/*': ['.png', '.jpg', '.jpeg', '.webp'] }}
              />
            ) : loading ? (
              <div className="flex items-center justify-center p-8">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 p-4">
                {images.map((image) => (
                  <button
                    key={image.id}
                    onClick={() => handleImageSelect(image.url)}
                    className="relative aspect-video rounded-lg overflow-hidden hover:ring-2 hover:ring-primary focus:ring-2 focus:ring-primary focus:outline-none transition-all"
                  >
                    <Image
                      src={image.url}
                      alt={image.title}
                      fill
                      className="object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
} 