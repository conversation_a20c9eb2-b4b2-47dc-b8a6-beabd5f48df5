'use client';

import { useState, useEffect, type FormEvent } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Newspaper, PlusCircle, Edit3, Trash2, Image as ImageIcon, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useForm, Controller, type SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import Image from 'next/image';

const newsSchema = z.object({
  title: z.string().min(5, { message: "يجب أن يكون العنوان 5 أحرف على الأقل" }),
  content: z.string().min(20, { message: "يجب أن يكون المحتوى 20 حرفًا على الأقل" }),
  author: z.string().min(3, { message: "يجب أن يكون اسم الكاتب 3 أحرف على الأقل" }),
  imageUrl: z.string().url({ message: "الرجاء إدخال رابط صورة صحيح" }).optional().or(z.literal('')),
});

type NewsArticle = z.infer<typeof newsSchema> & {
  id: number;
  date: string;
};

export default function AdminNewsPage() {
  const [newsItems, setNewsItems] = useState<NewsArticle[]>([]);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingNewsItem, setEditingNewsItem] = useState<NewsArticle | null>(null);
  const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false);
  const [deletingNewsItemId, setDeletingNewsItemId] = useState<number | null>(null);
  const { toast } = useToast();

  // Load news items on mount
  useEffect(() => {
    const fetchNews = async () => {
      try {
        const response = await fetch('/api/news');
        if (response.ok) {
          const data = await response.json();
          setNewsItems(data);
        }
      } catch (error) {
        console.error('Error fetching news:', error);
        toast({
          title: "خطأ",
          description: "حدث خطأ أثناء تحميل الأخبار",
          variant: "destructive"
        });
      }
    };

    fetchNews();
  }, []);

  const { register, handleSubmit, control, reset, setValue, formState: { errors, isSubmitting } } = useForm<z.infer<typeof newsSchema>>({
    resolver: zodResolver(newsSchema),
    defaultValues: { title: '', content: '', author: '', imageUrl: '' },
  });

  const handleAddNew = () => {
    reset({ title: '', content: '', author: '', imageUrl: '' });
    setEditingNewsItem(null);
    setIsAddModalOpen(true);
  };

  const handleEdit = (item: NewsArticle) => {
    setEditingNewsItem(item);
    setValue('title', item.title);
    setValue('content', item.content);
    setValue('author', item.author);
    setValue('imageUrl', item.imageUrl || '');
    setIsEditModalOpen(true);
  };

  const handleDelete = (id: number) => {
    setDeletingNewsItemId(id);
    setIsDeleteAlertOpen(true);
  };

  const confirmDelete = async () => {
    if (deletingNewsItemId !== null) {
      try {
        const response = await fetch('/api/news', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ id: deletingNewsItemId }),
        });

        if (response.ok) {
          setNewsItems(newsItems.filter(item => item.id !== deletingNewsItemId));
          toast({ title: "تم الحذف", description: "تم حذف الخبر بنجاح." });
        } else {
          throw new Error('Failed to delete news item');
        }
      } catch (error) {
        console.error('Error deleting news:', error);
        toast({
          title: "خطأ",
          description: "حدث خطأ أثناء حذف الخبر",
          variant: "destructive"
        });
      }
      setDeletingNewsItemId(null);
    }
    setIsDeleteAlertOpen(false);
  };

  const onSubmit: SubmitHandler<z.infer<typeof newsSchema>> = async (data) => {
    try {
      if (editingNewsItem) {
        // Edit existing item
        const response = await fetch('/api/news', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            id: editingNewsItem.id,
            ...data,
          }),
        });

        if (response.ok) {
          const updatedItem = await response.json();
          setNewsItems(newsItems.map(item => 
            item.id === editingNewsItem.id ? updatedItem : item
          ));
          toast({ title: "تم التعديل", description: "تم تعديل الخبر بنجاح." });
          setIsEditModalOpen(false);
        } else {
          throw new Error('Failed to update news item');
        }
      } else {
        // Add new item
        const response = await fetch('/api/news', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        if (response.ok) {
          const newItem = await response.json();
          setNewsItems([newItem, ...newsItems]);
          toast({ title: "تمت الإضافة", description: "تمت إضافة الخبر بنجاح." });
          setIsAddModalOpen(false);
        } else {
          throw new Error('Failed to add news item');
        }
      }
      reset();
    } catch (error) {
      console.error('Error saving news:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء حفظ الخبر",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <Newspaper className="h-6 w-6 text-primary" />
            <CardTitle>إدارة الأخبار</CardTitle>
          </div>
          <Button onClick={handleAddNew}>
            <PlusCircle className="ml-2 h-4 w-4" />
            إضافة خبر جديد
          </Button>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>صورة مصغرة</TableHead>
                <TableHead>العنوان</TableHead>
                <TableHead>الكاتب</TableHead>
                <TableHead>التاريخ</TableHead>
                <TableHead>الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {newsItems.length > 0 ? newsItems.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>
                    {item.imageUrl ? (
                      <Image src={item.imageUrl} alt={item.title} width={64} height={64} className="rounded object-cover h-16 w-16" data-ai-hint="news article" />
                    ) : (
                      <div className="h-16 w-16 bg-muted rounded flex items-center justify-center text-muted-foreground">
                        <ImageIcon className="h-8 w-8" />
                      </div>
                    )}
                  </TableCell>
                  <TableCell>{item.title}</TableCell>
                  <TableCell>{item.author}</TableCell>
                  <TableCell>{item.date}</TableCell>
                  <TableCell className="space-x-2 rtl:space-x-reverse">
                    <Button variant="outline" size="sm" onClick={() => handleEdit(item)}>
                      <Edit3 className="ml-1 h-4 w-4" /> تعديل
                    </Button>
                    <Button variant="destructive" size="sm" onClick={() => handleDelete(item.id)}>
                      <Trash2 className="ml-1 h-4 w-4" /> حذف
                    </Button>
                  </TableCell>
                </TableRow>
              )) : (
                <TableRow>
                  <TableCell colSpan={5} className="text-center">لا توجد أخبار لعرضها.</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add/Edit Modal */}
      <Dialog open={isAddModalOpen || isEditModalOpen} onOpenChange={editingNewsItem ? setIsEditModalOpen : setIsAddModalOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <form onSubmit={handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>{editingNewsItem ? 'تعديل الخبر' : 'إضافة خبر جديد'}</DialogTitle>
              <DialogDescription>
                {editingNewsItem ? 'قم بتعديل تفاصيل الخبر أدناه.' : 'أدخل تفاصيل الخبر الجديد.'}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="title" className="text-right col-span-1">العنوان</Label>
                <div className="col-span-3">
                  <Input id="title" {...register("title")} className={errors.title ? 'border-destructive' : ''} />
                  {errors.title && <p className="text-destructive text-sm mt-1">{errors.title.message}</p>}
                </div>
              </div>
              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="content" className="text-right col-span-1 pt-2">المحتوى</Label>
                <div className="col-span-3">
                  <Textarea id="content" {...register("content")} rows={5} className={errors.content ? 'border-destructive' : ''} />
                  {errors.content && <p className="text-destructive text-sm mt-1">{errors.content.message}</p>}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="author" className="text-right col-span-1">الكاتب</Label>
                <div className="col-span-3">
                  <Input id="author" {...register("author")} className={errors.author ? 'border-destructive' : ''} />
                  {errors.author && <p className="text-destructive text-sm mt-1">{errors.author.message}</p>}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="imageUrl" className="text-right col-span-1">رابط الصورة (اختياري)</Label>
                <div className="col-span-3">
                  <Input id="imageUrl" {...register("imageUrl")} placeholder="https://example.com/image.png" className={errors.imageUrl ? 'border-destructive' : ''} />
                   {errors.imageUrl && <p className="text-destructive text-sm mt-1">{errors.imageUrl.message}</p>}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => editingNewsItem ? setIsEditModalOpen(false) : setIsAddModalOpen(false)}>إلغاء</Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="ml-2 h-4 w-4 animate-spin rtl:mr-2" />}
                {editingNewsItem ? 'حفظ التعديلات' : 'إضافة الخبر'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteAlertOpen} onOpenChange={setIsDeleteAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد من حذف هذا الخبر؟</AlertDialogTitle>
            <AlertDialogDescription>
              لا يمكن التراجع عن هذا الإجراء بعد تنفيذه.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete}>تأكيد الحذف</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

    