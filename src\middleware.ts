import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { jwtVerify } from 'jose';

export async function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname;

  // Protect all admin routes
  if (path.startsWith('/admin')) {
    const token = request.cookies.get('auth')?.value;

    if (!token) {
      // Redirect to login if no token
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('redirect', path);
      return NextResponse.redirect(loginUrl);
    }

    try {
      // Verify the JWT token
      const encoder = new TextEncoder();
      const { payload } = await jwtVerify(
        token,
        encoder.encode(process.env.JWT_SECRET || 'your-secret-key')
      );
      
      // Type assertion for payload
      const userPayload = payload as { role: string; userId: string };
      
      if (userPayload.role !== 'ADMIN') {
        // Redirect non-admin users to homepage
        return NextResponse.redirect(new URL('/', request.url));
      }

      // Add user info to headers for server components
      const requestHeaders = new Headers(request.headers);
      requestHeaders.set('x-user-id', userPayload.userId);
      requestHeaders.set('x-user-role', userPayload.role);

      // Clone the response and add the headers
      const response = NextResponse.next({
        request: {
          headers: requestHeaders,
        },
      });

      return response;
    } catch (err) {
      // Invalid token, redirect to login
      console.error('Token verification failed:', err);
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('redirect', path);
      return NextResponse.redirect(loginUrl);
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/admin/:path*', // Protect all routes under /admin
  ],
};