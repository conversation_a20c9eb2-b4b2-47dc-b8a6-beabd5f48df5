@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 40 20% 94%; /* #F2F0EC - Off-white */
    --foreground: 20 15% 25%; /* Darker foreground for better contrast on off-white */

    --muted: 40 15% 88%; /* Slightly darker/muted version of background */
    --muted-foreground: 20 10% 45%; /* For text on muted backgrounds */

    --popover: 40 20% 94%;
    --popover-foreground: 20 15% 25%;

    --card: 40 20% 96%; /* Slightly brighter than background for cards */
    --card-foreground: 20 15% 25%;

    --border: 40 10% 80%; /* Border color derived from background */
    --input: 40 10% 85%; /* Input background */

    --primary: 45 38% 65%; /* #CDB77F - Muted Gold */
    --primary-foreground: 45 30% 15%; /* Dark brown/black for text on gold */

    --secondary: 14 28% 75%; /* Lighter shade of accent for secondary elements */
    --secondary-foreground: 14 30% 20%; /* Darker text for secondary */

    --accent: 14 28% 61%; /* #B78C7F - <PERSON> */
    --accent-foreground: 10 30% 96%; /* Off-white/light for text on accent */
    
    --destructive: 0 70% 50%; /* Standard destructive red */
    --destructive-foreground: 0 0% 98%;

    --ring: 45 38% 65%; /* Ring color to match primary */
    --radius: 0.5rem;

    /* Sidebar specific colors, can be adjusted if admin panel needs a different theme */
    --sidebar-background: 40 10% 90%;
    --sidebar-foreground: 20 15% 25%;
    --sidebar-primary: 45 38% 65%;
    --sidebar-primary-foreground: 45 30% 15%;
    --sidebar-accent: 14 28% 61%;
    --sidebar-accent-foreground: 10 30% 96%;
    --sidebar-border: 40 10% 80%;
    --sidebar-ring: 45 38% 65%;

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 20 15% 10%; 
    --foreground: 40 20% 94%; 

    --muted: 20 15% 15%;
    --muted-foreground: 40 10% 65%;

    --popover: 20 15% 10%;
    --popover-foreground: 40 20% 94%;

    --card: 20 15% 12%;
    --card-foreground: 40 20% 94%;

    --border: 20 10% 25%;
    --input: 20 10% 20%;

    --primary: 45 38% 65%; 
    --primary-foreground: 45 30% 15%;

    --secondary: 14 28% 50%; 
    --secondary-foreground: 10 30% 96%;

    --accent: 14 28% 61%; 
    --accent-foreground: 10 30% 96%;
    
    --destructive: 0 60% 50%;
    --destructive-foreground: 0 0% 98%;

    --ring: 45 38% 65%;

    --sidebar-background: 20 15% 12%;
    --sidebar-foreground: 40 20% 94%;
    --sidebar-primary: 45 38% 65%;
    --sidebar-primary-foreground: 45 30% 15%;
    --sidebar-accent: 14 28% 61%;
    --sidebar-accent-foreground: 10 30% 96%;
    --sidebar-border: 20 10% 25%;
    --sidebar-ring: 45 38% 65%;

    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    direction: rtl; /* Set default direction to RTL for Arabic */
    /* The font-family will now be applied via the font-sans utility class configured in tailwind.config.ts */
  }
}

@layer utilities {
  .bg-gradient-radial {
    background-image: radial-gradient(circle at center, var(--tw-gradient-stops));
  }
}

/* Custom animation for news ticker */
@keyframes scroll-left {
  0% {
    transform: translateX(0); /* Start with the first set of items visible */
  }
  100% {
    transform: translateX(-50%); /* Scroll by the width of one set of items. Assumes the animated element contains duplicated content and its total width is 200% of one set. */
  }
}

.animate-scroll-left {
  animation: scroll-left linear infinite; /* Default duration overridden by style prop */
}

/* Smooth transition for page content */
.fade-in-up {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.5s ease-out forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
