'use client';

import { useState, useEffect } from 'react';
import PageHeader from '@/components/layout/PageHeader';
import { Card, CardContent } from '@/components/ui/card';
import { Quote } from 'lucide-react';

interface PageContent {
  backgroundImage?: string;
}

interface Testimonial {
  id: string;
  name: string;
  content: string;
  rating: number;
  date: string;
}

export default function TestimonialsPage() {
  const [pageContent, setPageContent] = useState<PageContent>({});
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);

  useEffect(() => {
    const fetchPageContent = async () => {
      try {
        const response = await fetch('/api/pages');
        if (response.ok) {
          const data = await response.json();
          setPageContent(data.testimonials || {});
        }
      } catch (error) {
        console.error('Error fetching page content:', error);
      }
    };

    const fetchTestimonials = async () => {
      try {
        const response = await fetch('/api/testimonials');
        if (response.ok) {
          const data = await response.json();
          setTestimonials(data);
        }
      } catch (error) {
        console.error('Error fetching testimonials:', error);
      }
    };

    fetchPageContent();
    fetchTestimonials();
  }, []);

  return (
    <div className="space-y-16 md:space-y-20 fade-in-up">
      <PageHeader
        title="آراء العملاء"
        subtitle="نفخر بثقة عملائنا وآرائهم الإيجابية حول خدماتنا في رحلات الحج والعمرة."
        backgroundImage={pageContent.backgroundImage}
      />
      
      <section className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.map((testimonial, index) => (
            <Card key={testimonial.id} className="relative overflow-hidden">
              <CardContent className="p-6 space-y-4">
                <Quote className="h-8 w-8 text-primary/20 absolute top-4 right-4" />
                <div className="space-y-2">
                  <p className="text-foreground/90 leading-relaxed relative z-10">
                    {testimonial.content}
                  </p>
                  <div className="flex items-center justify-between pt-4">
                    <div>
                      <p className="font-semibold text-primary">{testimonial.name}</p>
                      <p className="text-sm text-muted-foreground">{testimonial.date}</p>
                    </div>
                    <div className="flex gap-1">
                      {Array.from({ length: testimonial.rating }).map((_, i) => (
                        <svg
                          key={i}
                          className="w-5 h-5 text-yellow-400"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>
    </div>
  );
} 