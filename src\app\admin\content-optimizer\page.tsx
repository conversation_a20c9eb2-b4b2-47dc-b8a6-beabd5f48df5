'use client';

import { useState, type FormEvent } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Lightbulb, Zap, Loader2 } from 'lucide-react';
import { improveWebsiteContent, type ImproveWebsiteContentInput, type ImproveWebsiteContentOutput } from '@/ai/flows/improve-website-content';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from '@/components/ui/separator';

export default function ContentOptimizerPage() {
  const [websiteContent, setWebsiteContent] = useState('');
  const [userInteractions, setUserInteractions] = useState('');
  const [userIntent, setUserIntent] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<ImproveWebsiteContentOutput | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setResult(null);
    setError(null);

    const input: ImproveWebsiteContentInput = {
      websiteContent,
      userInteractions,
      userIntent,
    };

    try {
      const output = await improveWebsiteContent(input);
      setResult(output);
    } catch (err) {
      console.error("Error improving website content:", err);
      setError(err instanceof Error ? err.message : "An unknown error occurred.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl flex items-center gap-2">
            <Lightbulb className="h-6 w-6 text-primary" />
            أداة تحسين محتوى الموقع باستخدام الذكاء الاصطناعي
          </CardTitle>
          <CardDescription>
            أدخل محتوى موقعك الحالي، ووصفًا لتفاعلات المستخدمين، وتقييمًا لنيتهم، للحصول على اقتراحات ذكية لتحسين المحتوى وزيادة التفاعل.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Label htmlFor="websiteContent" className="block text-md font-medium mb-1">محتوى الموقع الحالي:</Label>
              <Textarea
                id="websiteContent"
                value={websiteContent}
                onChange={(e) => setWebsiteContent(e.target.value)}
                placeholder="مثال: الصفحة الرئيسية لموقعنا تعرض خدمات الحج والعمرة، وتشمل وصفًا للباقات..."
                rows={6}
                required
                className="text-base"
              />
            </div>
            <div>
              <Label htmlFor="userInteractions" className="block text-md font-medium mb-1">وصف تفاعلات المستخدمين:</Label>
              <Textarea
                id="userInteractions"
                value={userInteractions}
                onChange={(e) => setUserInteractions(e.target.value)}
                placeholder="مثال: يزور المستخدمون صفحة الباقات، يقضون وقتًا أطول في باقة الحج المميزة، ولكن نسبة قليلة منهم تضغط على زر الحجز..."
                rows={4}
                required
                className="text-base"
              />
            </div>
            <div>
              <Label htmlFor="userIntent" className="block text-md font-medium mb-1">تقييم نية المستخدم:</Label>
              <Textarea
                id="userIntent"
                value={userIntent}
                onChange={(e) => setUserIntent(e.target.value)}
                placeholder="مثال: يبحث المستخدمون عن معلومات تفصيلية حول الأسعار والمميزات، ويقارنون بين الباقات المختلفة قبل اتخاذ قرار الحجز..."
                rows={3}
                required
                className="text-base"
              />
            </div>
            <Button type="submit" className="w-full sm:w-auto" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  جاري التحليل...
                </>
              ) : (
                <>
                  <Zap className="mr-2 h-4 w-4" />
                  احصل على اقتراحات التحسين
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      {error && (
        <Alert variant="destructive">
          <AlertTitle>خطأ في التحليل</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {result && (
        <Card className="shadow-lg mt-8 animate-in fade-in duration-500">
          <CardHeader>
            <CardTitle className="text-xl text-primary">نتائج التحليل والاقتراحات:</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-2">الاقتراحات المقترحة:</h3>
              <div className="p-4 bg-muted rounded-md whitespace-pre-wrap text-sm leading-relaxed">
                {result.suggestions}
              </div>
            </div>
            <Separator />
            <div>
              <h3 className="text-lg font-semibold mb-2">أساس الاقتراحات (المنطق):</h3>
              <div className="p-4 bg-muted rounded-md whitespace-pre-wrap text-sm leading-relaxed">
                {result.reasoning}
              </div>
            </div>
          </CardContent>
          <CardFooter>
             <Button variant="outline" onClick={() => {setResult(null); setError(null);}}>بدء تحليل جديد</Button>
          </CardFooter>
        </Card>
      )}
    </div>
  );
}
