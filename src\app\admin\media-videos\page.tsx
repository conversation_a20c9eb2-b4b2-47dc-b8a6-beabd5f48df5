'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Video, PlusCircle, Edit3, Trash2, PlayCircle, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { MediaCategory } from '@prisma/client';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const videoSchema = z.object({
  title: z.string().min(5, { message: "العنوان يجب أن يكون 5 أحرف على الأقل" }),
  videoUrl: z.string().url({ message: "الرجاء إدخال رابط فيديو صحيح (مثل YouTube, Vimeo)" }),
  thumbnailUrl: z.string().url({ message: "الرجاء إدخال رابط صورة مصغرة صحيح" }).optional().or(z.literal('')),
  description: z.string().optional().or(z.literal('')),
  category: z.nativeEnum(MediaCategory),
});

type VideoFormData = z.infer<typeof videoSchema>;

interface MediaVideo {
  id: string;
  title: string;
  url: string;
  thumbnailUrl?: string | null;
  description?: string | null;
  category: MediaCategory;
  created_at: string;
}

export default function AdminMediaVideosPage() {
  const [mediaVideos, setMediaVideos] = useState<MediaVideo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false);
  const [deletingVideoId, setDeletingVideoId] = useState<string | null>(null);
  const { toast } = useToast();

  const { register, handleSubmit, control, reset, setValue, formState: { errors, isSubmitting } } = useForm<VideoFormData>({
    resolver: zodResolver(videoSchema),
    defaultValues: {
      title: '',
      videoUrl: '',
      thumbnailUrl: '',
      description: '',
      category: MediaCategory.OTHER,
    },
  });

  useEffect(() => {
    fetchVideos();
  }, []);

  const fetchVideos = async () => {
    try {
      const response = await fetch('/api/media?type=VIDEO');
      if (!response.ok) throw new Error('Failed to fetch videos');
      const data = await response.json();
      setMediaVideos(data);
    } catch (error) {
      toast({
        title: "خطأ في تحميل الفيديوهات",
        description: "حدث خطأ أثناء تحميل الفيديوهات. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddNew = () => {
    reset({
      title: '',
      videoUrl: '',
      thumbnailUrl: '',
      description: '',
      category: MediaCategory.OTHER,
    });
    setIsAddModalOpen(true);
  };

  const handleEdit = (video: MediaVideo) => {
    setValue('title', video.title);
    setValue('videoUrl', video.url);
    setValue('thumbnailUrl', video.thumbnailUrl || '');
    setValue('description', video.description || '');
    setValue('category', video.category);
    setIsEditModalOpen(true);
  };

  const handleDelete = (id: string) => {
    setDeletingVideoId(id);
    setIsDeleteAlertOpen(true);
  };

  const confirmDelete = async () => {
    if (deletingVideoId) {
      try {
        const response = await fetch(`/api/media/${deletingVideoId}`, {
          method: 'DELETE',
        });

        if (!response.ok) throw new Error('Failed to delete video');

        setMediaVideos(mediaVideos.filter(vid => vid.id !== deletingVideoId));
        toast({
          title: "تم الحذف",
          description: "تم حذف الفيديو بنجاح.",
        });
      } catch (error) {
        toast({
          title: "خطأ في الحذف",
          description: "فشل حذف الفيديو. يرجى المحاولة مرة أخرى.",
          variant: "destructive",
        });
      }
    }
    setIsDeleteAlertOpen(false);
    setDeletingVideoId(null);
  };

  const onSubmit = async (data: VideoFormData) => {
    try {
      const formData = new FormData();
      formData.append('title', data.title);
      formData.append('type', 'VIDEO');
      formData.append('videoUrl', data.videoUrl);
      formData.append('thumbnailUrl', data.thumbnailUrl || '');
      formData.append('description', data.description || '');
      formData.append('category', data.category);

      const response = await fetch('/api/media/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) throw new Error('Failed to add video');

      const newVideo = await response.json();
      setMediaVideos([newVideo, ...mediaVideos]);
      toast({
        title: "تمت الإضافة",
        description: "تم إضافة الفيديو بنجاح.",
      });
      setIsAddModalOpen(false);
      reset();
    } catch (error) {
      toast({
        title: "خطأ في الإضافة",
        description: "فشلت إضافة الفيديو. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    }
  };

  const getVideoThumbnail = (video: MediaVideo) => {
    if (video.thumbnailUrl) return video.thumbnailUrl;
    if (video.url.includes('youtube.com') || video.url.includes('youtu.be')) {
      const videoId = video.url.split('v=')[1]?.split('&')[0] || video.url.split('/').pop();
      return videoId ? `https://img.youtube.com/vi/${videoId}/mqdefault.jpg` : 'https://placehold.co/120x90.png?text=فيديو';
    }
    return 'https://placehold.co/120x90.png?text=فيديو';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <Video className="h-6 w-6 text-primary" />
            <CardTitle>إدارة الفيديوهات</CardTitle>
          </div>
          <Button onClick={handleAddNew}>
            <PlusCircle className="ml-2 h-4 w-4" />
            إضافة فيديو جديد
          </Button>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>مصغرة</TableHead>
                <TableHead>العنوان</TableHead>
                <TableHead>الفئة</TableHead>
                <TableHead>تاريخ الإضافة</TableHead>
                <TableHead>الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mediaVideos.length > 0 ? mediaVideos.map((video) => (
                <TableRow key={video.id}>
                  <TableCell>
                    <div className="relative w-24 h-16 rounded overflow-hidden bg-muted">
                      <img 
                        src={getVideoThumbnail(video)} 
                        alt={video.title} 
                        className="w-full h-full object-cover"
                      />
                      <a href={video.url} target="_blank" rel="noopener noreferrer" className="absolute inset-0 flex items-center justify-center bg-black/30 opacity-0 hover:opacity-100 transition-opacity">
                        <PlayCircle className="h-8 w-8 text-white" />
                      </a>
                    </div>
                  </TableCell>
                  <TableCell>{video.title}</TableCell>
                  <TableCell>{getCategoryLabel(video.category)}</TableCell>
                  <TableCell>{new Date(video.created_at).toLocaleDateString('ar-SA')}</TableCell>
                  <TableCell className="space-x-2 rtl:space-x-reverse">
                    <Button variant="outline" size="sm" onClick={() => handleEdit(video)}>
                      <Edit3 className="ml-1 h-4 w-4" /> تعديل
                    </Button>
                    <Button variant="destructive" size="sm" onClick={() => handleDelete(video.id)}>
                      <Trash2 className="ml-1 h-4 w-4" /> حذف
                    </Button>
                  </TableCell>
                </TableRow>
              )) : (
                <TableRow>
                  <TableCell colSpan={5} className="text-center">لا توجد فيديوهات لعرضها.</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add/Edit Modal */}
      <Dialog open={isAddModalOpen || isEditModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <form onSubmit={handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>إضافة فيديو جديد</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div>
                <Label htmlFor="title">عنوان الفيديو</Label>
                <Input id="title" {...register("title")} className={errors.title ? 'border-destructive' : ''} />
                {errors.title && <p className="text-destructive text-sm mt-1">{errors.title.message}</p>}
              </div>
              <div>
                <Label htmlFor="videoUrl">رابط الفيديو (YouTube, Vimeo, etc.)</Label>
                <Input id="videoUrl" {...register("videoUrl")} placeholder="https://www.youtube.com/watch?v=..." className={errors.videoUrl ? 'border-destructive' : ''} />
                {errors.videoUrl && <p className="text-destructive text-sm mt-1">{errors.videoUrl.message}</p>}
              </div>
              <div>
                <Label htmlFor="thumbnailUrl">رابط الصورة المصغرة (اختياري)</Label>
                <Input id="thumbnailUrl" {...register("thumbnailUrl")} placeholder="https://example.com/thumbnail.jpg" className={errors.thumbnailUrl ? 'border-destructive' : ''} />
                {errors.thumbnailUrl && <p className="text-destructive text-sm mt-1">{errors.thumbnailUrl.message}</p>}
              </div>
              <div>
                <Label htmlFor="category">الفئة</Label>
                <Select {...register("category")}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الفئة" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(MediaCategory).map((category) => (
                      <SelectItem key={category} value={category}>
                        {getCategoryLabel(category)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.category && <p className="text-destructive text-sm mt-1">{errors.category.message}</p>}
              </div>
              <div>
                <Label htmlFor="description">الوصف (اختياري)</Label>
                <Textarea id="description" {...register("description")} rows={3} className={errors.description ? 'border-destructive' : ''} />
                {errors.description && <p className="text-destructive text-sm mt-1">{errors.description.message}</p>}
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsAddModalOpen(false)}>إلغاء</Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="ml-2 h-4 w-4 animate-spin rtl:mr-2" />}
                إضافة الفيديو
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Alert */}
      <AlertDialog open={isDeleteAlertOpen} onOpenChange={setIsDeleteAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد من حذف هذا الفيديو؟</AlertDialogTitle>
            <AlertDialogDescription>
              هذا الإجراء لا يمكن التراجع عنه. سيتم حذف الفيديو نهائياً.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              حذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

function getCategoryLabel(category: MediaCategory): string {
  const labels = {
    [MediaCategory.PREVIOUS_TRIP]: 'رحلات سابقة',
    [MediaCategory.GALLERY]: 'معرض الصور',
    [MediaCategory.NEWS]: 'الأخبار',
    [MediaCategory.OTHER]: 'أخرى'
  };
  return labels[category] || category;
}
