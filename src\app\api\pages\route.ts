import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

const ContentType = {
  PAGE: 'PAGE'
} as const;

type PageContent = {
  backgroundImage?: string;
  [key: string]: any;
};

interface ContentRecord {
  id: string;
  type: string;
  title: string;
  content: string;
  created_at: Date;
  updated_at: Date;
}

export async function GET() {
  try {
    const pages = await prisma.content.findMany({
      where: {
        type: ContentType.PAGE
      }
    });

    // Convert array to record
    const pageContents = pages.reduce<Record<string, PageContent>>((acc: Record<string, PageContent>, page: ContentRecord) => {
      try {
        const content = JSON.parse(page.content) as PageContent;
        acc[page.title] = content;
      } catch (e) {
        console.error(`Error parsing content for page ${page.title}:`, e);
      }
      return acc;
    }, {});

    return NextResponse.json(pageContents);
  } catch (error) {
    console.error('Error fetching page contents:', error);
    return NextResponse.json(
      { error: 'Error fetching page contents' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { pageKey, content } = await request.json() as { pageKey: string; content: PageContent };
    console.log('Received update request:', { pageKey, content });

    if (!pageKey || !content) {
      console.error('Missing required fields:', { pageKey, content });
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Update or create page content
    const page = await prisma.content.upsert({
      where: {
        type_title: {
          type: ContentType.PAGE,
          title: pageKey
        }
      },
      update: {
        content: JSON.stringify(content)
      },
      create: {
        type: ContentType.PAGE,
        title: pageKey,
        content: JSON.stringify(content)
      }
    });

    console.log('Updated page content:', page);
    return NextResponse.json(page);
  } catch (error) {
    console.error('Error updating page content:', error);
    // Return more detailed error information
    return NextResponse.json(
      { 
        error: 'Error updating page content',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
} 