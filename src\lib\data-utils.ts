import fs from 'fs';
import path from 'path';

const DATA_DIR = path.join(process.cwd(), 'public', 'data');

// Ensure the data directory exists
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}

export async function saveData<T>(filename: string, data: T): Promise<void> {
  const filePath = path.join(DATA_DIR, filename);
  await fs.promises.writeFile(filePath, JSON.stringify(data, null, 2));
}

export async function loadData<T>(filename: string, defaultData: T): Promise<T> {
  const filePath = path.join(DATA_DIR, filename);
  try {
    if (fs.existsSync(filePath)) {
      const rawData = await fs.promises.readFile(filePath, 'utf-8');
      return JSON.parse(rawData) as T;
    }
    // If file doesn't exist, save and return default data
    await saveData(filename, defaultData);
    return defaultData;
  } catch (error) {
    console.error(`Error loading data from ${filename}:`, error);
    return defaultData;
  }
} 