'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import type { User } from '@prisma/client';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  isAdmin: false,
});

export const useAuth = () => useContext(AuthContext);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState<AuthContextType>({
    user: null,
    loading: true,
    isAdmin: false
  });

  useEffect(() => {
    async function checkAuth() {
      try {
        const res = await fetch('/api/auth/check', {
          credentials: 'include'
        });
        const data = await res.json();

        if (data.success) {
          setState({ 
            user: data.user, 
            loading: false,
            isAdmin: data.user.role === 'ADMIN'
          });
        } else {
          setState({ 
            user: null, 
            loading: false,
            isAdmin: false
          });
        }
      } catch (error) {
        setState({ 
          user: null, 
          loading: false,
          isAdmin: false
        });
      }
    }

    checkAuth();
  }, []);

  return (
    <AuthContext.Provider value={state}>
      {children}
    </AuthContext.Provider>
  );
}