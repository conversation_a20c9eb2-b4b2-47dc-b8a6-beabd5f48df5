'use client';

import { useState, useCallback } from 'react';
import Image from 'next/image';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, X } from 'lucide-react';

interface ImageSliderProps {
  images: Array<{
    id: string;
    url: string;
    title?: string;
    description?: string;
  }>;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialIndex?: number;
}

export function ImageSlider({ images, open, onOpenChange, initialIndex = 0 }: ImageSliderProps) {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);

  if (!images || images.length === 0) {
    return null;
  }

  const handlePrevious = useCallback((e?: React.MouseEvent) => {
    e?.stopPropagation();
    setCurrentIndex((prev) => (prev > 0 ? prev - 1 : images.length - 1));
  }, [images.length]);

  const handleNext = useCallback((e?: React.MouseEvent) => {
    e?.stopPropagation();
    setCurrentIndex((prev) => (prev < images.length - 1 ? prev + 1 : 0));
  }, [images.length]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    e.stopPropagation();
    if (e.key === 'ArrowRight') handlePrevious();
    if (e.key === 'ArrowLeft') handleNext();
    if (e.key === 'Escape') onOpenChange(false);
  }, [handlePrevious, handleNext, onOpenChange]);

  const safeIndex = Math.min(Math.max(0, currentIndex), images.length - 1);
  const currentImage = images[safeIndex];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent 
        className="max-w-7xl w-full h-[90vh] p-0 bg-background/95 backdrop-blur"
        onKeyDown={handleKeyDown}
      >
        <DialogTitle className="sr-only">
          معرض الصور
        </DialogTitle>
        <div className="relative w-full h-full flex items-center justify-center">
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-4 right-4 z-50 hover:bg-background/20"
            onClick={() => onOpenChange(false)}
          >
            <X className="h-6 w-6" />
          </Button>

          {images.length > 1 && (
            <>
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-4 top-1/2 -translate-y-1/2 hover:bg-background/20"
                onClick={handlePrevious}
              >
                <ChevronRight className="h-8 w-8" />
                <span className="sr-only">السابق</span>
              </Button>

              <Button
                variant="ghost"
                size="icon"
                className="absolute left-4 top-1/2 -translate-y-1/2 hover:bg-background/20"
                onClick={handleNext}
              >
                <ChevronLeft className="h-8 w-8" />
                <span className="sr-only">التالي</span>
              </Button>
            </>
          )}

          <div className="relative w-full h-full p-12">
            <Image
              src={currentImage.url}
              alt={currentImage.title || ''}
              fill
              className="object-contain"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
              priority
            />
          </div>

          <div className="absolute bottom-4 left-0 right-0 text-center bg-background/80 py-2 px-4">
            <h3 className="text-lg font-semibold">{currentImage.title}</h3>
            {currentImage.description && (
              <p className="text-sm text-muted-foreground mt-1">{currentImage.description}</p>
            )}
            {images.length > 1 && (
              <p className="text-sm text-muted-foreground mt-2">
                {safeIndex + 1} / {images.length}
              </p>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 