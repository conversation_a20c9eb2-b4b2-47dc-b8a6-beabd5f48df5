'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { UsersRound, Target, Eye, Award, Package, ShieldCheck, Hotel } from 'lucide-react';
import PageHeader from '@/components/layout/PageHeader';

interface PageContent {
  backgroundImage?: string;
}

export default function AboutPage() {
  const [pageContent, setPageContent] = useState<PageContent>({});

  useEffect(() => {
    const fetchPageContent = async () => {
      try {
        console.log('Fetching about page content...');
        const response = await fetch('/api/pages');
        if (response.ok) {
          const data = await response.json();
          console.log('All pages data:', data);
          console.log('About page data:', data.about);
          setPageContent(data.about || {});
        } else {
          console.error('Failed to fetch page content:', await response.text());
        }
      } catch (error) {
        console.error('Error fetching page content:', error);
      }
    };

    fetchPageContent();
  }, []);

  console.log('Current page content:', pageContent);

  return (
    <div className="space-y-16 md:space-y-20 fade-in-up">
      <PageHeader
        title="من نحن"
        subtitle="شركة إيثار لخدمات حجاج الداخل، رفيقكم الموثوق في رحلتكم الإيمانية نحو الأراضي المقدسة."
        backgroundImage={pageContent.backgroundImage}
      />

      <section className="grid md:grid-cols-2 gap-10">
        <Card className="shadow-xl h-full flex flex-col">
          <CardHeader className="text-center">
            <Target className="h-20 w-20 text-accent mx-auto mb-6" />
            <CardTitle className="text-3xl text-primary">رسالتنا</CardTitle>
          </CardHeader>
          <CardContent className="text-center flex-grow">
            <p className="text-lg text-foreground leading-relaxed">
              نسعى في إيثار لتقديم خدمات حج وعمرة متميزة تتسم بالاحترافية والمصداقية، مع الحرص على توفير كافة سبل الراحة لضيوف الرحمن ليؤدوا مناسكهم في جو من الطمأنينة والروحانية العالية.
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-xl h-full flex flex-col">
          <CardHeader className="text-center">
            <Eye className="h-20 w-20 text-accent mx-auto mb-6" />
            <CardTitle className="text-3xl text-primary">رؤيتنا</CardTitle>
          </CardHeader>
          <CardContent className="text-center flex-grow">
            <p className="text-lg text-foreground leading-relaxed">
              أن نكون الشركة الرائدة في مجال خدمات الحج والعمرة على مستوى المملكة، من خلال الابتكار المستمر في خدماتنا والالتزام بأعلى معايير الجودة لإرضاء عملائنا الكرام.
            </p>
          </CardContent>
        </Card>
      </section>
      
      <section className="grid md:grid-cols-2 gap-10 items-center py-12">
        <div className="order-last md:order-first">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-8">لماذا تختار إيثار؟</h2>
          <ul className="space-y-6 text-lg text-muted-foreground">
            {[
              { icon: Award, text: "خبرة تمتد لسنوات في خدمة ضيوف الرحمن." },
              { icon: UsersRound, text: "فريق عمل مؤهل ومتفانٍ في خدمتكم على مدار الساعة." },
              { icon: Package, text: "باقات متنوعة تلبي كافة الاحتياجات والميزانيات." },
              { icon: ShieldCheck, text: "التزام تام بالشفافية والمصداقية في جميع تعاملاتنا." },
              { icon: Hotel, text: "مواقع سكن مميزة وقريبة من الحرمين الشريفين." },
            ].map((item, index) => {
              const IconComponent = item.icon;
              return (
                <li key={index} className="flex items-start">
                  <div className="bg-primary/10 p-2 rounded-full mr-4 rtl:ml-4 mt-1 flex-shrink-0">
                    <IconComponent className="h-6 w-6 text-primary" />
                  </div>
                  <span className="leading-relaxed">{item.text}</span>
                </li>
              );
            })}
          </ul>
        </div>
        <div className="rounded-xl overflow-hidden shadow-2xl">
          <Image 
            src="https://placehold.co/600x400.png" 
            alt="فريق إيثار المتفاني" 
            width={600} 
            height={400} 
            className="w-full h-auto object-cover transform hover:scale-105 transition-transform duration-300"
            data-ai-hint="team work" 
          />
        </div>
      </section>

      <section className="text-center py-16 bg-gradient-to-b from-muted/30 to-background rounded-xl shadow-inner">
        <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-12">قيمنا الأساسية</h2>
        <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-8 max-w-5xl mx-auto">
          {['الإخلاص', 'الأمانة', 'الجودة', 'الضيافة'].map((value) => (
            <Card key={value} className="bg-card p-8 shadow-lg hover:shadow-2xl transition-shadow duration-300 transform hover:-translate-y-1">
              <CardTitle className="text-2xl text-primary">{value}</CardTitle>
            </Card>
          ))}
        </div>
      </section>
    </div>
  );
}
