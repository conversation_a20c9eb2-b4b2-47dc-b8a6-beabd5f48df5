import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    const content = await prisma.content.findFirst({
      where: {
        type: 'HOMEPAGE'
      }
    });

    if (!content) {
      return NextResponse.json({
        hero: {
          backgroundImageUrl: '',
          backgroundOpacity: 0.5,
          titleColor: '#FFFFFF',
          subtitleColor: '#FFFFFF',
          dataAiHint: 'kaaba mecca',
          title: 'مرحباً بكم في إيثار لخدمات الحجاج',
          subtitle: 'نقدم لكم أفضل تجربة حج وعمرة مع خدمات متكاملة ورعاية فائقة لضمان راحتكم وأداء مناسككم بيسر وسهولة.',
        },
        latestTrips: []
      });
    }

    const contentData = JSON.parse(content.content);

    return NextResponse.json({
      hero: {
        backgroundImageUrl: contentData.backgroundImageUrl || '',
        backgroundOpacity: contentData.backgroundOpacity ?? 0.5,
        titleColor: contentData.titleColor || '#FFFFFF',
        subtitleColor: contentData.subtitleColor || '#FFFFFF',
        dataAiHint: contentData.dataAiHint || 'kaaba mecca',
        title: content.title || 'مرحباً بكم في إيثار لخدمات الحجاج',
        subtitle: contentData.subtitle || 'نقدم لكم أفضل تجربة حج وعمرة مع خدمات متكاملة ورعاية فائقة لضمان راحتكم وأداء مناسككم بيسر وسهولة.',
      },
      latestTrips: []
    });
  } catch (error) {
    console.error('Error fetching homepage content:', error);
    return NextResponse.json(
      { error: 'Failed to fetch homepage content' },
      { status: 500 }
    );
  }
} 