import { Cairo } from 'next/font/google';
import './globals.css';
import { AppProviders } from '@/components/layout/AppProviders';
import RootLayoutContent from '@/components/layout/RootLayoutContent';

const cairo = Cairo({
  subsets: ['arabic', 'latin'],
  display: 'swap',
  variable: '--font-cairo',
  weight: ['400', '500', '600', '700'],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl" className={cairo.className}>
      <body className="antialiased">
        <AppProviders>
          <RootLayoutContent>
            {children}
          </RootLayoutContent>
        </AppProviders>
      </body>
    </html>
  );
}
