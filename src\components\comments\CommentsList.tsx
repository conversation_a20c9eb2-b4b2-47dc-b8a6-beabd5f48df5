'use client';

import { useState, useEffect } from 'react';
import { fetchComments } from '@/lib/api-utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';
import { AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface Comment {
  id: number;
  author: string;
  content: string;
  date: string;
  status: 'pending' | 'approved' | 'rejected';
}

export default function CommentsList() {
  const [comments, setComments] = useState<Comment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  useEffect(() => {
    const loadComments = async () => {
      try {
        setError(null);
        const data = await fetchComments();
        if (!Array.isArray(data)) {
          throw new Error('Invalid data format received');
        }
        // Only show approved comments
        setComments(data.filter((comment: Comment) => comment.status === 'approved'));
        setRetryCount(0); // Reset retry count on successful load
      } catch (error) {
        console.error('Error loading comments:', error);
        setError('حدث خطأ أثناء تحميل التعليقات');
        // Implement exponential backoff for retries
        if (retryCount < 3) {
          setTimeout(() => {
            setRetryCount(prev => prev + 1);
          }, Math.pow(2, retryCount) * 1000); // Wait 1s, 2s, 4s between retries
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadComments();
    // تحديث التعليقات كل 30 ثانية
    const interval = setInterval(loadComments, 30 * 1000);
    return () => clearInterval(interval);
  }, [retryCount]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (comments.length === 0) {
    return (
      <Card>
        <CardContent className="py-6 text-center text-muted-foreground">
          لا توجد تعليقات حتى الآن
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {comments.map((comment) => (
        <Card key={comment.id}>
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <Avatar>
                <AvatarFallback>{comment.author[0].toUpperCase()}</AvatarFallback>
              </Avatar>
              <div>
                <CardTitle className="text-sm font-medium">{comment.author}</CardTitle>
                <p className="text-xs text-muted-foreground">
                  {formatDistanceToNow(new Date(comment.date), { addSuffix: true, locale: ar })}
                </p>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm">{comment.content}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
} 