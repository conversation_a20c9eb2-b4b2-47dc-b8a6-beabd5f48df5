import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { sendUpdateToAll } from '../updates/route';

export const dynamic = 'force-dynamic';

// Helper function to parse JSON values
function parseSettingValue(value: string | null, type: string) {
  if (!value) return null;
  if (type === 'JSON') {
    try {
      return JSON.parse(value);
    } catch {
      return null;
    }
  }
  if (type === 'NUMBER') return Number(value);
  if (type === 'BOOLEAN') return value === 'true';
  return value;
}

// Helper function to format settings into a structured object
function formatSettings(settings: any[]) {
  const formatted: any = {
    id: 'contact-settings',
    contact: {},
    social: {},
    map: {}
  };
  
  settings.forEach(setting => {
    const value = parseSettingValue(setting.value, setting.type);
    
    switch (setting.group) {
      case 'CONTACT':
        formatted[setting.name] = value;
        formatted.contact[setting.name] = value;
        break;
      case 'SOCIAL':
        formatted[setting.name] = value;
        formatted.social[setting.name] = value;
        break;
      case 'MAP':
        if (setting.name === 'location') {
          formatted.mapLocation = value;
        }
        formatted.map[setting.name] = value;
        break;
      default:
        formatted[setting.name] = value;
    }
  });

  return formatted;
}

export async function GET() {
  try {
    const settings = await prisma.setting.findMany();
    const formattedSettings = settings.reduce((acc, setting) => {
      acc[setting.name] = setting.value;
      return acc;
    }, {} as Record<string, string>);

    return new Response(JSON.stringify(formattedSettings), {
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error fetching settings:', error);
    return new Response(
      JSON.stringify({
        error: 'Failed to fetch settings',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
}

export async function PUT(request: Request) {
  try {
    const data = await request.json();
    console.log('Received data:', data);
    
    // Prepare settings updates
    const updates = [];
    
    // General Settings
    if (data.siteName !== undefined) {
      updates.push(prisma.setting.upsert({
        where: { name: 'siteName' },
        create: {
          name: 'siteName',
          value: data.siteName
        },
        update: {
          value: data.siteName
        }
      }));
    }

    if (data.siteDescription !== undefined) {
      updates.push(prisma.setting.upsert({
        where: { name: 'siteDescription' },
        create: {
          name: 'siteDescription',
          value: data.siteDescription
        },
        update: {
          value: data.siteDescription
        }
      }));
    }

    if (data.siteLogo !== undefined) {
      updates.push(prisma.setting.upsert({
        where: { name: 'siteLogo' },
        create: {
          name: 'siteLogo',
          value: data.siteLogo
        },
        update: {
          value: data.siteLogo
        }
      }));
    }

    // Email Settings
    const emailSettings = [
      'smtpHost',
      'smtpPort',
      'smtpUser',
      'smtpPassword',
      'smtpFromEmail',
      'smtpFromName'
    ];

    emailSettings.forEach(setting => {
      if (data[setting] !== undefined) {
        updates.push(prisma.setting.upsert({
          where: { name: setting },
          create: {
            name: setting,
            value: data[setting] || '' // Handle null values
          },
          update: {
            value: data[setting] || '' // Handle null values
          }
        }));
      }
    });
    
    // Contact Information
    if (data.phone !== undefined) {
      updates.push(prisma.setting.upsert({
        where: { name: 'phone' },
        create: {
          name: 'phone',
          value: data.phone || '' // Handle null values
        },
        update: {
          value: data.phone || '' // Handle null values
        }
      }));
    }
    
    if (data.email !== undefined) {
      updates.push(prisma.setting.upsert({
        where: { name: 'email' },
        create: {
          name: 'email',
          value: data.email
        },
        update: {
          value: data.email
        }
      }));
    }
    
    if (data.address !== undefined) {
      updates.push(prisma.setting.upsert({
        where: { name: 'address' },
        create: {
          name: 'address',
          value: data.address
        },
        update: {
          value: data.address
        }
      }));
    }
    
    if (data.workingHours !== undefined) {
      updates.push(prisma.setting.upsert({
        where: { name: 'workingHours' },
        create: {
          name: 'workingHours',
          value: data.workingHours
        },
        update: {
          value: data.workingHours
        }
      }));
    }
    
    if (data.whatsapp !== undefined) {
      updates.push(prisma.setting.upsert({
        where: { name: 'whatsapp' },
        create: {
          name: 'whatsapp',
          value: data.whatsapp
        },
        update: {
          value: data.whatsapp
        }
      }));
    }
    
    // Social Media Links
    const socialLinks = ['facebook', 'twitter', 'instagram', 'youtube', 'website'];
    socialLinks.forEach(platform => {
      if (data[platform] !== undefined) {
        updates.push(prisma.setting.upsert({
          where: { name: platform },
          create: {
            name: platform,
            value: data[platform]
          },
          update: {
            value: data[platform]
          }
        }));
      }
    });
    
    // Map Location
    if (data.mapLocation) {
      updates.push(prisma.setting.upsert({
        where: { name: 'location' },
        create: { 
          name: 'location', 
          value: JSON.stringify(data.mapLocation)
        },
        update: { 
          value: JSON.stringify(data.mapLocation)
        }
      }));
    }
    
    console.log('Prepared updates:', updates);
    
    // Execute all updates
    await prisma.$transaction(updates);
    
    // Send update notification
    sendUpdateToAll();
    
    // Fetch and return updated settings
    const updatedSettings = await prisma.setting.findMany();
    return NextResponse.json(formatSettings(updatedSettings));
  } catch (error) {
    console.error('Error updating settings:', error);
    return NextResponse.json(
      { error: 'Error updating settings', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
} 