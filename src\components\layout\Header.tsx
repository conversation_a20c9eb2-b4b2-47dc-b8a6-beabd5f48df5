'use client';

import Link from 'next/link';
import { Menu, X, Moon, Sun, LogOut } from 'lucide-react'; 
import { useState } from 'react';
import { NAV_LINKS, NavItem } from '@/lib/constants';
import NewsTicker from './NewsTicker';
import { Button } from '@/components/ui/button';
import { useTheme } from 'next-themes'; 
import { useAuth } from '@/components/auth/useAuth';
import { useSiteSettings } from '@/hooks/use-site-settings';
import Image from 'next/image';
import { cn } from '@/lib/utils';

export default function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { theme, setTheme } = useTheme();
  const { isAdmin, logout } = useAuth();
  const { settings } = useSiteSettings();

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const renderNavLinks = (links: NavItem[]) => {
    return links.map((link) => (
      <Link
        key={link.href}
        href={link.href}
        className={cn(
          "px-4 py-2 rounded-lg text-sm font-bold transition-colors",
          "hover:bg-accent hover:text-accent-foreground",
          "focus:bg-accent focus:text-accent-foreground focus:outline-none"
        )}
      >
        {link.label}
      </Link>
    ));
  };

  return (
    <header className="bg-background sticky top-0 z-50">
      <div className="border-b border-border/40">
        <NewsTicker />
      </div>
      <div className="border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-24">
            <Link 
              href="/" 
              className="relative group py-2"
              aria-label="الصفحة الرئيسية"
            >
              {settings?.siteLogo ? (
                <div className="relative w-20 h-20 overflow-hidden rounded-xl transition-all duration-300 transform group-hover:scale-105">
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent rounded-xl" />
                  <Image
                    src={settings.siteLogo}
                    alt="الشعار"
                    fill
                    className="object-contain p-3"
                    priority
                    sizes="(max-width: 768px) 60px, 80px"
                  />
                  <div className="absolute inset-0 border border-border/10 rounded-xl group-hover:border-primary/20 transition-colors duration-300" />
                </div>
              ) : null}
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-2 rtl:space-x-reverse">
              {renderNavLinks(NAV_LINKS.filter(link => !link.admin))}
              {isAdmin && (
                <>
                  <div className="h-4 w-px bg-border mx-2" />
                  {renderNavLinks(NAV_LINKS.filter(link => link.admin))}
                </>
              )}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                className="ml-2"
              >
                {theme === 'dark' ? (
                  <Sun className="h-5 w-5" />
                ) : (
                  <Moon className="h-5 w-5" />
                )}
              </Button>
              {isAdmin && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={logout}
                  className="ml-2"
                >
                  <LogOut className="h-5 w-5" />
                </Button>
              )}
            </nav>

            {/* Mobile Menu Button */}
            <div className="md:hidden flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
              >
                {theme === 'dark' ? (
                  <Sun className="h-5 w-5" />
                ) : (
                  <Moon className="h-5 w-5" />
                )}
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleMobileMenu}
                aria-label="Toggle Menu"
              >
                {isMobileMenuOpen ? (
                  <X className="h-5 w-5" />
                ) : (
                  <Menu className="h-5 w-5" />
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMobileMenuOpen && (
        <nav className="md:hidden border-b border-border/40">
          <div className="container mx-auto px-4 py-4 space-y-2">
            {renderNavLinks(NAV_LINKS.filter(link => !link.admin))}
            {isAdmin && (
              <>
                <div className="h-px bg-border my-4" />
                {renderNavLinks(NAV_LINKS.filter(link => link.admin))}
                <Button
                  variant="ghost"
                  onClick={logout}
                  className="w-full justify-start"
                >
                  <LogOut className="h-5 w-5 ml-2" />
                  تسجيل الخروج
                </Button>
              </>
            )}
          </div>
        </nav>
      )}
    </header>
  );
}
