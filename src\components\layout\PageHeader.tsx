'use client';

import { cn } from "@/lib/utils";
import { useEffect } from "react";

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  backgroundImage?: string;
  className?: string;
}

export default function PageHeader({
  title,
  subtitle,
  backgroundImage,
  className
}: PageHeaderProps) {
  useEffect(() => {
    console.log('PageHeader rendered with background:', backgroundImage);
  }, [backgroundImage]);

  return (
    <section className={cn(
      "relative py-20 md:py-32 bg-gradient-to-br from-background via-muted/30 to-background rounded-xl overflow-hidden shadow-inner",
      className
    )}>
      {backgroundImage && (
        <div 
          className="absolute inset-0 bg-cover bg-center opacity-20 transition-opacity duration-700" 
          style={{
            backgroundImage: `url('${backgroundImage}')`,
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          }}
        />
      )}
      <div className="absolute inset-0 bg-gradient-to-t from-background via-transparent to-transparent opacity-60" />
      <div className="relative container mx-auto px-4 z-10 text-center">
        <h1 className="text-4xl md:text-6xl font-bold text-primary mb-6 fade-in-up">{title}</h1>
        {subtitle && (
          <p className="text-lg md:text-xl text-foreground mb-10 max-w-3xl mx-auto fade-in-up" style={{animationDelay: '100ms'}}>
            {subtitle}
          </p>
        )}
      </div>
    </section>
  );
} 