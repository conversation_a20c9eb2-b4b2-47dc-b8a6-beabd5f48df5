import { PrismaClient } from '@prisma/client';
import { hash } from 'bcryptjs';

async function resetAdmin() {
  const prisma = new PrismaClient();
  const email = '<EMAIL>';
  const password = 'admin123';

  try {
    // Hash password with 12 rounds
    const hashedPassword = await hash(password, 12);

    // Upsert admin user
    const user = await prisma.user.upsert({
      where: { email },
      update: {
        password: hashedPassword,
      },
      create: {
        email,
        password: hashedPassword,
        role: 'ADMIN',
      },
    });

    console.log('Admin user reset successfully:', {
      id: user.id,
      email: user.email,
      role: user.role,
    });
  } catch (error) {
    console.error('Error resetting admin:', error);
  } finally {
    await prisma.$disconnect();
  }
}

resetAdmin(); 