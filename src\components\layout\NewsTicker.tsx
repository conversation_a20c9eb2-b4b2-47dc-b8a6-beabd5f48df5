'use client';

import { useEffect, useState } from 'react';
import Marque<PERSON> from 'react-fast-marquee';
import { Circle } from 'lucide-react';

interface NewsTickerItem {
  id: string;
  text: string;
  link?: string;
  icon?: string;
}

interface NewsTickerData {
  items: NewsTickerItem[];
  speed: number;
}

export default function NewsTicker() {
  const [newsTickerData, setNewsTickerData] = useState<NewsTickerData>({
    items: [],
    speed: 30
  });

  const fetchNewsTicker = async () => {
    try {
      const response = await fetch('/api/news-ticker');
      if (response.ok) {
        const data = await response.json();
        setNewsTickerData(data);
      }
    } catch (error) {
      console.error('Error fetching news ticker:', error);
    }
  };

  useEffect(() => {
    fetchNewsTicker();

    // Set up WebSocket connection
    const ws = new WebSocket(`${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/api/updates`);
    
    ws.onmessage = () => {
      // Refresh news ticker when update is received
      fetchNewsTicker();
    };

    return () => {
      ws.close();
    };
  }, []);

  if (newsTickerData.items.length === 0) {
    return null;
  }

  return (
    <div className="bg-primary text-primary-foreground py-2 overflow-hidden">
      <Marquee
        speed={newsTickerData.speed}
        gradient={false}
        className="flex items-center"
      >
        {newsTickerData.items.map((item, index) => (
          <span key={item.id} className="flex items-center">
            {index > 0 && (
              <Circle className="mx-4 h-1.5 w-1.5 shrink-0 fill-current opacity-50" />
            )}
            <span className="whitespace-nowrap text-base">
              {item.link ? (
                <a 
                  href={item.link} 
                  className="hover:underline transition-colors hover:text-primary-foreground/80"
                >
                  {item.text}
                </a>
              ) : (
                item.text
              )}
            </span>
          </span>
        ))}
      </Marquee>
    </div>
  );
}
