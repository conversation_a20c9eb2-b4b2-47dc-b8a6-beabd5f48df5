import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PlusCircle, X, Upload, Trash2, Loader2 } from 'lucide-react';
import Image from 'next/image';
import { useToast } from '@/hooks/use-toast';

export interface Package {
  id: string;
  title: string;
  description: string;
  price: string;
  features: string[];
  imageUrl: string;
  duration: string;
}

interface PackageEditorProps {
  packages: Package[];
  onSave: (packages: Package[]) => void;
  type: 'hajj' | 'umrah';
}

export default function PackageEditor({ packages: initialPackages, onSave, type }: PackageEditorProps) {
  const [packages, setPackages] = useState<Package[]>(initialPackages);
  const [uploading, setUploading] = useState<{ [key: string]: boolean }>({});
  const { toast } = useToast();

  const addPackage = () => {
    const newPackage: Package = {
      id: Date.now().toString(),
      title: '',
      description: '',
      price: '',
      features: [''],
      imageUrl: '',
      duration: '',
    };
    setPackages([...packages, newPackage]);
  };

  const updatePackage = (index: number, field: keyof Package, value: any) => {
    const updatedPackages = [...packages];
    updatedPackages[index] = {
      ...updatedPackages[index],
      [field]: value,
    };
    setPackages(updatedPackages);
  };

  const addFeature = (packageIndex: number) => {
    const updatedPackages = [...packages];
    updatedPackages[packageIndex].features.push('');
    setPackages(updatedPackages);
  };

  const updateFeature = (packageIndex: number, featureIndex: number, value: string) => {
    const updatedPackages = [...packages];
    updatedPackages[packageIndex].features[featureIndex] = value;
    setPackages(updatedPackages);
  };

  const removeFeature = (packageIndex: number, featureIndex: number) => {
    const updatedPackages = [...packages];
    updatedPackages[packageIndex].features.splice(featureIndex, 1);
    setPackages(updatedPackages);
  };

  const removePackage = (index: number) => {
    const updatedPackages = [...packages];
    updatedPackages.splice(index, 1);
    setPackages(updatedPackages);
  };

  const handleSave = () => {
    onSave(packages);
  };

  const handleImageUpload = async (packageIndex: number, file: File) => {
    try {
      setUploading({ ...uploading, [packageIndex]: true });
      
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Error uploading file');
      }

      updatePackage(packageIndex, 'imageUrl', data.url);
      toast({
        title: "تم رفع الصورة",
        description: "تم رفع الصورة بنجاح",
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء رفع الصورة",
        variant: "destructive",
      });
    } finally {
      setUploading({ ...uploading, [packageIndex]: false });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">
          {type === 'hajj' ? 'باقات الحج' : 'باقات العمرة'}
        </h3>
        <Button onClick={addPackage}>
          <PlusCircle className="ml-2 h-4 w-4" />
          إضافة باقة جديدة
        </Button>
      </div>

      <div className="grid gap-6">
        {packages.map((pkg, packageIndex) => (
          <Card key={pkg.id} className="relative">
            <Button
              variant="destructive"
              size="icon"
              className="absolute left-2 top-2"
              onClick={() => removePackage(packageIndex)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
            
            <CardHeader>
              <CardTitle className="text-xl">الباقة {packageIndex + 1}</CardTitle>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>عنوان الباقة</Label>
                  <Input
                    value={pkg.title}
                    onChange={(e) => updatePackage(packageIndex, 'title', e.target.value)}
                    placeholder="مثال: باقة الحج الاقتصادية"
                  />
                </div>
                <div>
                  <Label>السعر</Label>
                  <Input
                    value={pkg.price}
                    onChange={(e) => updatePackage(packageIndex, 'price', e.target.value)}
                    placeholder="مثال: 15,000 ريال"
                  />
                </div>
              </div>

              <div>
                <Label>المدة</Label>
                <Input
                  value={pkg.duration}
                  onChange={(e) => updatePackage(packageIndex, 'duration', e.target.value)}
                  placeholder="مثال: 15 يوم"
                />
              </div>

              <div>
                <Label>وصف الباقة</Label>
                <Textarea
                  value={pkg.description}
                  onChange={(e) => updatePackage(packageIndex, 'description', e.target.value)}
                  placeholder="اكتب وصفاً مختصراً للباقة..."
                  rows={3}
                />
              </div>

              <div>
                <Label>صورة الباقة</Label>
                <div className="flex gap-4 items-end">
                  <div className="flex-grow space-y-2">
                    <div className="flex gap-2">
                      <Input
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            handleImageUpload(packageIndex, file);
                          }
                        }}
                        className="flex-grow"
                      />
                      {uploading[packageIndex] && (
                        <Button disabled>
                          <Loader2 className="h-4 w-4 animate-spin" />
                        </Button>
                      )}
                    </div>
                    {pkg.imageUrl && (
                      <div className="text-sm text-muted-foreground">
                        الصورة الحالية: {pkg.imageUrl}
                      </div>
                    )}
                  </div>
                  {pkg.imageUrl && (
                    <div className="relative w-20 h-20 shrink-0">
                      <Image
                        src={pkg.imageUrl}
                        alt={pkg.title}
                        fill
                        className="object-cover rounded-md"
                      />
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label>المميزات</Label>
                {pkg.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex gap-2">
                    <Input
                      value={feature}
                      onChange={(e) => updateFeature(packageIndex, featureIndex, e.target.value)}
                      placeholder="أدخل ميزة..."
                    />
                    <Button
                      variant="destructive"
                      size="icon"
                      onClick={() => removeFeature(packageIndex, featureIndex)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button variant="outline" onClick={() => addFeature(packageIndex)}>
                  <PlusCircle className="ml-2 h-4 w-4" />
                  إضافة ميزة
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="flex justify-end">
        <Button onClick={handleSave} className="w-full md:w-auto">
          حفظ التغييرات
        </Button>
      </div>
    </div>
  );
} 