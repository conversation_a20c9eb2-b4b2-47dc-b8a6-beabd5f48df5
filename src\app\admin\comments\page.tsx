'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { MessageSquare, Trash2, CheckCircle, XCircle, Eye, PlusCircle, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

type CommentStatus = 'pending' | 'approved' | 'rejected';

interface Comment {
  id: number;
  author: string;
  email: string;
  content: string;
  date: string;
  status: CommentStatus;
  postTitle?: string;
}

const commentSchema = z.object({
  author: z.string().min(3, { message: 'اسم الكاتب يجب أن يكون 3 أحرف على الأقل' }),
  email: z.string().email({ message: 'يرجى إدخال بريد إلكتروني صحيح' }),
  content: z.string().min(10, { message: 'التعليق يجب أن يكون 10 أحرف على الأقل' }),
  postTitle: z.string().optional()
});

export default function AdminCommentsPage() {
  const [comments, setComments] = useState<Comment[]>([]);
  const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false);
  const [deletingCommentId, setDeletingCommentId] = useState<number | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [viewingComment, setViewingComment] = useState<Comment | null>(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  const { register, handleSubmit, reset, formState: { errors, isSubmitting } } = useForm<z.infer<typeof commentSchema>>({
    resolver: zodResolver(commentSchema)
  });

  // Load comments
  useEffect(() => {
    fetchComments();
  }, []);

  const fetchComments = async () => {
    try {
      const response = await fetch('/api/comments');
      const data = await response.json();
      setComments(data);
    } catch (error) {
      console.error('Error fetching comments:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء تحميل التعليقات",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = (id: number) => {
    setDeletingCommentId(id);
    setIsDeleteAlertOpen(true);
  };

  const confirmDelete = async () => {
    if (deletingCommentId !== null) {
      try {
        const response = await fetch('/api/comments', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ id: deletingCommentId }),
        });

        if (response.ok) {
          setComments(comments.filter(comment => comment.id !== deletingCommentId));
          toast({ title: "تم الحذف", description: "تم حذف التعليق بنجاح." });
        } else {
          throw new Error('Failed to delete comment');
        }
      } catch (error) {
        console.error('Error deleting comment:', error);
        toast({
          title: "خطأ",
          description: "حدث خطأ أثناء حذف التعليق",
          variant: "destructive",
        });
      }
      setDeletingCommentId(null);
    }
    setIsDeleteAlertOpen(false);
  };

  const onSubmit = async (data: z.infer<typeof commentSchema>) => {
    try {
      const newComment = {
        ...data,
        date: new Date().toISOString(),
        status: 'pending' as CommentStatus,
      };

      const response = await fetch('/api/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newComment),
      });

      if (response.ok) {
        const savedComment = await response.json();
        setComments([savedComment, ...comments]);
        toast({ title: "تمت الإضافة", description: "تم إضافة التعليق بنجاح." });
        setIsAddModalOpen(false);
        reset();
      } else {
        throw new Error('Failed to add comment');
      }
    } catch (error) {
      console.error('Error adding comment:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء إضافة التعليق",
        variant: "destructive",
      });
    }
  };

  const updateCommentStatus = async (id: number, status: CommentStatus) => {
    const comment = comments.find(c => c.id === id);
    if (!comment) return;

    try {
      const response = await fetch('/api/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ...comment, status }),
      });

      if (response.ok) {
        setComments(comments.map(c => c.id === id ? { ...c, status } : c));
        const statusText = status === 'approved' ? 'الموافقة على' : status === 'rejected' ? 'رفض' : 'تحديث حالة';
        toast({ title: "تم التحديث", description: `تم ${statusText} التعليق بنجاح.` });
      } else {
        throw new Error('Failed to update comment status');
      }
    } catch (error) {
      console.error('Error updating comment status:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء تحديث حالة التعليق",
        variant: "destructive",
      });
    }
  };
  
  const handleViewComment = (comment: Comment) => {
    setViewingComment(comment);
    setIsViewModalOpen(true);
  };

  const getStatusBadgeVariant = (status: CommentStatus) => {
    switch (status) {
      case 'approved': return 'default';
      case 'pending': return 'secondary';
      case 'rejected': return 'destructive';
      default: return 'outline';
    }
  };
  
  const getStatusText = (status: CommentStatus) => {
    switch (status) {
      case 'approved': return 'مقبول';
      case 'pending': return 'قيد المراجعة';
      case 'rejected': return 'مرفوض';
      default: return status;
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <MessageSquare className="h-6 w-6 text-primary" />
            <CardTitle>إدارة التعليقات</CardTitle>
          </div>
          <Button onClick={() => setIsAddModalOpen(true)}>
            <PlusCircle className="ml-2 h-4 w-4" />
            إضافة تعليق جديد
          </Button>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>الكاتب</TableHead>
                <TableHead>التعليق (مقتطف)</TableHead>
                <TableHead>على المقال/الصفحة</TableHead>
                <TableHead>التاريخ</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead>الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {comments.length > 0 ? comments.map((comment) => (
                <TableRow key={comment.id}>
                  <TableCell>{comment.author} <br/><span className="text-xs text-muted-foreground">{comment.email}</span></TableCell>
                  <TableCell className="max-w-xs truncate">{comment.content}</TableCell>
                  <TableCell>{comment.postTitle || 'عام'}</TableCell>
                  <TableCell>{new Date(comment.date).toLocaleDateString('ar-SA')}</TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(comment.status)}>{getStatusText(comment.status)}</Badge>
                  </TableCell>
                  <TableCell className="space-x-1 rtl:space-x-reverse">
                    <Button variant="ghost" size="icon" onClick={() => handleViewComment(comment)} title="عرض التعليق كاملاً">
                      <Eye className="h-4 w-4" />
                    </Button>
                    {comment.status !== 'approved' && (
                      <Button variant="ghost" size="icon" onClick={() => updateCommentStatus(comment.id, 'approved')} title="موافقة">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      </Button>
                    )}
                    {comment.status !== 'rejected' && (
                       <Button variant="ghost" size="icon" onClick={() => updateCommentStatus(comment.id, 'rejected')} title="رفض">
                        <XCircle className="h-4 w-4 text-orange-600" />
                      </Button>
                    )}
                    <Button variant="ghost" size="icon" onClick={() => handleDelete(comment.id)} title="حذف">
                      <Trash2 className="h-4 w-4 text-destructive" />
                    </Button>
                  </TableCell>
                </TableRow>
              )) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center">لا توجد تعليقات لعرضها.</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add Comment Modal */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <form onSubmit={handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>إضافة تعليق جديد</DialogTitle>
              <DialogDescription>أضف تعليقاً جديداً من خلال النموذج التالي</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="author">اسم الكاتب</Label>
                <Input
                  id="author"
                  {...register('author')}
                  className={errors.author ? 'border-destructive' : ''}
                />
                {errors.author && (
                  <p className="text-sm text-destructive">{errors.author.message}</p>
                )}
              </div>
              <div className="grid gap-2">
                <Label htmlFor="email">البريد الإلكتروني</Label>
                <Input
                  id="email"
                  type="email"
                  {...register('email')}
                  className={errors.email ? 'border-destructive' : ''}
                />
                {errors.email && (
                  <p className="text-sm text-destructive">{errors.email.message}</p>
                )}
              </div>
              <div className="grid gap-2">
                <Label htmlFor="content">التعليق</Label>
                <Textarea
                  id="content"
                  {...register('content')}
                  rows={4}
                  className={errors.content ? 'border-destructive' : ''}
                />
                {errors.content && (
                  <p className="text-sm text-destructive">{errors.content.message}</p>
                )}
              </div>
              <div className="grid gap-2">
                <Label htmlFor="postTitle">المقال/الصفحة (اختياري)</Label>
                <Input id="postTitle" {...register('postTitle')} />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsAddModalOpen(false)}>
                إلغاء
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="ml-2 h-4 w-4 animate-spin rtl:mr-2" />}
                إضافة التعليق
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* View Comment Modal */}
      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>عرض التعليق</DialogTitle>
            {viewingComment && <DialogDescription>تعليق من: {viewingComment.author} ({viewingComment.email})</DialogDescription>}
          </DialogHeader>
          {viewingComment && (
            <div className="py-4 space-y-2">
              <p><strong>المقال/الصفحة:</strong> {viewingComment.postTitle || 'عام'}</p>
              <p><strong>التاريخ:</strong> {new Date(viewingComment.date).toLocaleDateString('ar-SA')}</p>
              <p><strong>الحالة:</strong> <Badge variant={getStatusBadgeVariant(viewingComment.status)}>{getStatusText(viewingComment.status)}</Badge></p>
              <p className="text-md bg-muted p-3 rounded-md whitespace-pre-wrap">{viewingComment.content}</p>
            </div>
          )}
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsViewModalOpen(false)}>إغلاق</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteAlertOpen} onOpenChange={setIsDeleteAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد من حذف التعليق؟</AlertDialogTitle>
            <AlertDialogDescription>
              هذا الإجراء لا يمكن التراجع عنه. سيؤدي هذا إلى حذف التعليق بشكل دائم.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete}>نعم، قم بالحذف</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

    