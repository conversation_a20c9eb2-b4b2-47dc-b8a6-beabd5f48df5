# إعداد نظام إدارة المحتوى

## متطلبات النظام
1. XAMPP (Apache + MySQL)
2. Node.js (الإصدار 18 أو أحدث)
3. NPM (مدير حزم Node.js)

## خطوات الإعداد

### 1. إعد<PERSON> قاعدة البيانات
1. قم بتشغيل XAMPP وتفعيل خدمتي Apache و MySQL
2. افتح phpMyAdmin من خلال http://localhost/phpmyadmin
3. قم بإنشاء قاعدة بيانات جديدة باسم `ithaar_db`
4. استورد ملف قاعدة البيانات:
   - انتقل إلى تبويب "Import"
   - اختر الملف `src/config/database.sql`
   - اضغط على "Go" لتنفيذ الاستيراد

### 2. إعداد متغيرات البيئة
قم بتعديل ملف `.env` بالقيم المناسبة:
```env
# Database Configuration
DB_HOST="localhost"
DB_USER="root"
DB_PASSWORD=""
DB_NAME="ithaar_db"

# Site Configuration
NEXT_PUBLIC_SITE_URL="http://localhost:9002"
NEXT_PUBLIC_COMPANY_NAME="إيثار"

# JWT Secret for Authentication
JWT_SECRET="ithaar-secret-key-change-in-production"
```

### 3. تثبيت اعتماديات المشروع
```bash
npm install
```

### 4. تشغيل المشروع
```bash
npm run dev
```
سيتم تشغيل المشروع على المنفذ 9002: http://localhost:9002

## إنشاء حساب المدير الأول
1. قم بتعديل بيانات المدير في قاعدة البيانات مباشرة أو استخدم السكربت:
```bash
npx tsx src/scripts/create-admin.ts
```

## تسجيل الدخول
1. اذهب إلى صفحة تسجيل الدخول: http://localhost:9002/login
2. استخدم بيانات الدخول:
   - البريد الإلكتروني: <EMAIL>
   - كلمة المرور: Ithaar@2024

## ملاحظات هامة
- قم بتغيير `JWT_SECRET` في الإنتاج إلى قيمة آمنة وعشوائية
- قم بتغيير كلمة مرور المدير بعد أول تسجيل دخول
- احرص على عمل نسخة احتياطية لقاعدة البيانات بشكل دوري