import * as React from "react";

// This file can be used to export custom SVG icons or re-export lucide icons for consistency.

// Example of a Kaaba icon (simple placeholder)
export const KaabaIcon = () => React.createElement(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round"
  },
  React.createElement("path", { d: "M4 4h16v16H4z" }), // Outer square
  React.createElement("path", { d: "M12 2v2" }),       // Top line
  React.createElement("path", { d: "M12 20v2" }),      // Bottom line
  React.createElement("path", { d: "M2 12h2" }),       // Left line
  React.createElement("path", { d: "M20 12h2" }),      // Right line
  React.createElement("path", { d: "M7 7h10v10H7z" }), // Inner square
  React.createElement("circle", { cx: "12", cy: "12", r: "1" }) // Center dot
);

// Re-export commonly used lucide icons for easier access if desired
export {
    Users,
    Briefcase,
    Star,
    ShieldCheck,
    Home,
    Image,
    MessageSquareHeart,
    HelpCircle,
    Phone,
    Menu,
    X,
    Moon,
    Sun
} from 'lucide-react';
