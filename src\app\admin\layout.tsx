'use client';

import { usePathname } from 'next/navigation';
import { Sidebar } from './components/sidebar';
import { useAuth } from '@/components/auth/useAuth';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Toaster } from "@/components/ui/toaster";

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const { user, loading, logout } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push(`/login?redirect=${pathname}`);
    }
  }, [loading, user, router, pathname]);

  if (loading || !user) {
    return null; // Or show a loading spinner
  }

  return (
    <div className="flex min-h-screen">
      <Sidebar className="w-64 border-l" />
      <main className="flex-1 p-8">
        {children}
      </main>
      <Toaster />
    </div>
  );
}
