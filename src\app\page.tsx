'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Briefcase, Users, Star, Plane, Hotel, ImageIcon } from 'lucide-react'; 
import { KaabaIcon } from '@/components/icons';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/client/avatar';
import { fetchComments } from '@/lib/api-utils';
import { MediaType, MediaCategory } from '@prisma/client';

const FeatureCard = ({ icon, title, description, link, animationDelay }: { icon: React.ReactNode, title: string, description: string, link?: string, animationDelay?: string }) => (
  <Card className="text-center shadow-lg hover:shadow-xl transition-shadow duration-300 h-full flex flex-col fade-in-up" style={{animationDelay}}>
    <CardHeader>
      <div className="mx-auto bg-primary/10 text-primary p-4 rounded-full w-fit mb-4">
        {icon}
      </div>
      <CardTitle className="text-xl font-semibold">{title}</CardTitle>
    </CardHeader>
    <CardContent className="flex-grow">
      <CardDescription>{description}</CardDescription>
    </CardContent>
    {link && (
      <CardFooter className="pt-0">
        <Link href={link} passHref className="w-full">
          <Button variant="outline" className="w-full border-primary text-primary hover:bg-primary/10">معرفة المزيد</Button>
        </Link>
      </CardFooter>
    )}
  </Card>
);

const ServiceHighlightCard = ({ icon, title, description, link, animationDelay }: { icon: React.ReactNode, title: string, description: string, link: string, animationDelay?: string }) => (
  <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 h-full flex flex-col text-center fade-in-up" style={{animationDelay}}>
    <CardHeader>
      <div className="mx-auto text-accent p-3 rounded-full bg-accent/10 w-fit mb-3">
        {icon}
      </div>
      <CardTitle className="text-xl font-semibold text-primary">{title}</CardTitle>
    </CardHeader>
    <CardContent className="flex-grow">
      <p className="text-muted-foreground">{description}</p>
    </CardContent>
    <CardFooter>
      <Link href={link} passHref className="w-full">
        <Button variant="default" className="w-full">اكتشف الباقات</Button>
      </Link>
    </CardFooter>
  </Card>
);

interface Comment {
  id: number;
  author: string;
  content: string;
  date: string;
  status: 'pending' | 'approved' | 'rejected';
}

interface HomepageData {
  hero: {
    backgroundImageUrl: string;
    backgroundOpacity: number;
    titleColor: string;
    subtitleColor: string;
    dataAiHint: string;
    title: string;
    subtitle: string;
  };
  latestTrips: Array<{
    id: number;
    imageUrl: string;
    dataAiHint: string;
    title: string;
    description: string;
  }>;
}

interface MediaGroup {
  id: string;
  title: string;
  description?: string;
  category: MediaCategory;
  items: Array<{
    id: string;
    url: string;
    title: string;
    description?: string;
  }>;
}

const TestimonialCard = ({ comment, animationDelay }: { comment: Comment, animationDelay?: string }) => (
  <Card className="bg-background p-6 rounded-lg shadow-md fade-in-up border border-primary/20" style={{animationDelay}}>
    <CardContent className="text-center">
      <Avatar className="w-20 h-20 mx-auto mb-4 border-2 border-primary">
        <AvatarFallback className="text-2xl bg-primary/10 text-primary">{comment.author[0].toUpperCase()}</AvatarFallback>
      </Avatar>
      <p className="text-foreground italic mb-3 text-md">&ldquo;{comment.content}&rdquo;</p>
      <p className="font-semibold text-accent">{comment.author}</p>
    </CardContent>
  </Card>
);

export default function Home() {
  const [comments, setComments] = useState<Comment[]>([]);
  const [homepageData, setHomepageData] = useState<HomepageData | null>(null);
  const [mediaGroups, setMediaGroups] = useState<MediaGroup[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isBackgroundLoaded, setIsBackgroundLoaded] = useState(false);

  const loadData = async () => {
    try {
      // Fetch homepage content from the database
      const response = await fetch('/api/homepage');
      if (!response.ok) throw new Error('Failed to fetch homepage data');
      const data = await response.json();
      setHomepageData(data);

      // Load comments
      const commentsData = await fetchComments();
      setComments(
        commentsData
          .filter((comment: Comment) => comment.status === 'approved')
          .sort((a: Comment, b: Comment) => new Date(b.date).getTime() - new Date(a.date).getTime())
          .slice(0, 2)
      );

      // Load media for previous trips
      const mediaResponse = await fetch('/api/media?type=IMAGE&category=PREVIOUS_TRIP', {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
      if (!mediaResponse.ok) throw new Error('Failed to fetch media');
      const mediaData = await mediaResponse.json();
      
      // Group media items
      const groups = mediaData.reduce((acc: { [key: string]: MediaGroup }, item: any) => {
        const groupId = item.groupId || item.id;
        if (!acc[groupId]) {
          acc[groupId] = {
            id: groupId,
            title: item.groupTitle || item.title,
            description: item.description,
            category: item.category,
            items: []
          };
        }
        acc[groupId].items.push({
          id: item.id,
          url: item.url,
          title: item.title,
          description: item.description
        });
        return acc;
      }, {});

      setMediaGroups(Object.values(groups));
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadData();

    // إعداد SSE للتحديثات المباشرة
    const eventSource = new EventSource('/api/updates');

    eventSource.onmessage = (event) => {
      if (event.data === 'update') {
        loadData(); // إعادة تحميل البيانات عند استلام تحديث
      }
    };

    eventSource.onerror = (error) => {
      console.error('SSE Error:', error);
      eventSource.close();
    };

    return () => {
      eventSource.close();
    };
  }, []);

  if (!homepageData || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-20 md:space-y-28">
      {/* Hero Section */}
      <section className="relative text-center py-20 md:py-32 bg-gradient-to-br from-background via-muted/30 to-background rounded-xl overflow-hidden shadow-inner fade-in-up">
        {homepageData.hero.backgroundImageUrl && (
          <>
            <div 
              className={`absolute inset-0 bg-cover bg-center transition-opacity duration-700 ${isBackgroundLoaded ? '' : 'opacity-0'}`}
              style={{
                backgroundImage: `url('${homepageData.hero.backgroundImageUrl}')`,
                filter: 'brightness(1.2) contrast(1.2)',
                opacity: homepageData.hero.backgroundOpacity
              }}
              data-ai-hint={homepageData.hero.dataAiHint}
            />
            <div className="absolute inset-0 bg-gradient-radial from-transparent via-background/20 to-background opacity-50" />
            <Image
              src={homepageData.hero.backgroundImageUrl}
              alt={homepageData.hero.title}
              fill
              className="hidden"
              onLoad={() => setIsBackgroundLoaded(true)}
              priority
              quality={100}
              sizes="100vw"
            />
          </>
        )}
        <div className="absolute inset-0 bg-gradient-to-t from-background via-transparent to-transparent opacity-30" />
        <div className="relative container mx-auto px-4 z-10">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 fade-in-up" style={{
            animationDelay: '100ms',
            textShadow: '0 2px 4px rgba(0,0,0,0.2)',
            color: homepageData.hero.titleColor
          }}>
            {homepageData.hero.title}
          </h1>
          <p className="text-lg md:text-xl font-medium mb-10 max-w-3xl mx-auto fade-in-up" style={{
            animationDelay: '200ms',
            textShadow: '0 2px 4px rgba(0,0,0,0.2)',
            color: homepageData.hero.subtitleColor
          }}>
            {homepageData.hero.subtitle}
          </p>
          <div className="space-x-4 rtl:space-x-reverse fade-in-up" style={{animationDelay: '300ms'}}>
            <Link href="/services" passHref>
              <Button size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground px-10 py-3 text-lg shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105">
                اكتشف باقاتنا
              </Button>
            </Link>
            <Link href="/contact" passHref>
              <Button size="lg" variant="outline" className="border-primary text-primary hover:bg-primary hover:text-primary-foreground px-10 py-3 text-lg shadow-sm hover:shadow-md transition-all duration-300 hover:scale-105">
                تواصل معنا
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Our Features Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-16 text-foreground fade-in-up">لماذا تختار إيثار؟</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-10">
            <FeatureCard
              icon={<KaabaIcon />} 
              title="باقات حج وعمرة متكاملة"
              description="نوفر باقات متنوعة تناسب جميع الاحتياجات والميزانيات، مع التركيز على الجودة والراحة."
              link="/services"
              animationDelay="100ms"
            />
            <FeatureCard
              icon={<Users className="w-10 h-10" />}
              title="فريق عمل متخصص"
              description="فريق من ذوي الخبرة والكفاءة العالية لمرافقتكم وخدمتكم طوال الرحلة."
              link="/about"
              animationDelay="200ms"
            />
            <FeatureCard
              icon={<Star className="w-10 h-10" />}
              title="رضا العملاء أولويتنا"
              description="نسعى دائماً لتحقيق أعلى مستويات الرضا لعملائنا الكرام من خلال خدماتنا المتميزة."
              link="/reviews"
              animationDelay="300ms"
            />
          </div>
        </div>
      </section>

      {/* Services Highlight Section */}
      <section className="py-16 bg-muted/50 rounded-xl shadow-sm">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-16 text-foreground fade-in-up">خدماتنا المتكاملة لرحلتكم الإيمانية</h2>
          <div className="grid md:grid-cols-3 gap-10">
            <ServiceHighlightCard
              icon={<KaabaIcon />}
              title="باقات حج شاملة"
              description="تصاميم متنوعة تناسب جميع الاحتياجات والميزانيات لضمان أداء مناسك الحج بيسر وسكينة."
              link="/services#hajj"
              animationDelay="100ms"
            />
            <ServiceHighlightCard
              icon={<Plane className="w-10 h-10" />}
              title="رحلات عمرة مباركة"
              description="ننظم رحلات عمرة على مدار العام مع توفير كافة سبل الراحة لتجربة روحانية لا تُنسى."
              link="/services#umrah"
              animationDelay="200ms"
            />
            <ServiceHighlightCard
              icon={<Hotel className="w-10 h-10" />}
              title="إقامة فاخرة ومريحة"
              description="نختار لكم أفضل الفنادق القريبة من الحرمين الشريفين لضمان إقامة هادئة ومريحة."
              link="/services"
              animationDelay="300ms"
            />
          </div>
        </div>
      </section>

      {/* Latest Trips Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-16 text-foreground fade-in-up">من رحلاتنا السابقة</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {mediaGroups.slice(0, 4).map((group, index) => (
              <Card key={group.id} className="overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 ease-out hover:scale-105 fade-in-up" style={{animationDelay: `${index * 100 + 100}ms`}}>
                <div className="relative h-52 w-full">
                  <Image 
                    src={group.items[0].url}
                    alt={group.title}
                    fill
                    sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
                    className="object-cover"
                  />
                  {group.items.length > 1 && (
                    <div className="absolute bottom-2 right-2 bg-background/80 px-2 py-1 rounded text-sm">
                      <ImageIcon className="h-4 w-4 inline-block ml-1" />
                      {group.items.length}
                    </div>
                  )}
                </div>
                <CardContent className="p-5">
                  <h3 className="font-semibold text-lg mb-2 text-foreground">{group.title}</h3>
                  {group.description && (
                    <p className="text-sm text-muted-foreground line-clamp-3">{group.description}</p>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="text-center mt-16 fade-in-up" style={{animationDelay: '500ms'}}>
            <Link href="/media" passHref>
              <Button variant="outline" size="lg" className="border-primary text-primary hover:bg-primary/10 px-8 py-3 text-lg shadow-sm hover:shadow-md">
                عرض كل الصور والفيديوهات
              </Button>
            </Link>
          </div>
        </div>
      </section>
      
      {/* Testimonials Section */}
      <section className="py-16 bg-gradient-to-b from-background via-accent/5 to-background rounded-xl">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-16 text-foreground fade-in-up">شهادات نعتز بها من ضيوف الرحمن</h2>
          <div className="grid md:grid-cols-2 gap-10 max-w-4xl mx-auto">
            {comments.map((comment, index) => (
              <TestimonialCard
                key={comment.id}
                comment={comment}
                animationDelay={`${(index + 1) * 100}ms`}
              />
            ))}
          </div>
          <div className="text-center mt-16 fade-in-up" style={{animationDelay: '300ms'}}>
            <Link href="/reviews" passHref>
              <Button variant="default" size="lg" className="px-8 py-3 text-lg shadow-md hover:shadow-lg">
                اقرأ المزيد من الآراء
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-muted rounded-xl shadow-sm">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-foreground mb-6 fade-in-up">هل أنت مستعد لرحلة إيمانية لا تُنسى؟</h2>
          <p className="text-lg text-muted-foreground mb-10 max-w-xl mx-auto fade-in-up" style={{animationDelay: '100ms'}}>
            دعنا نساعدك في التخطيط لرحلة الحج أو العمرة القادمة. تواصل معنا اليوم للحصول على استشارة مجانية.
          </p>
          <Link href="/contact" passHref>
            <Button size="lg" className="bg-primary hover:bg-primary/80 text-primary-foreground px-12 py-4 text-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 fade-in-up" style={{animationDelay: '200ms'}}>
              احجز استشارتك الآن
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
}

    