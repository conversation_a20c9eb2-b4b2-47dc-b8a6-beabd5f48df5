import { NextResponse } from 'next/server';

// Store connected clients
const clients = new Set<ReadableStreamDefaultController>();

// Function to send updates to all connected clients
export function sendUpdateToAll() {
  clients.forEach(client => {
    try {
      client.enqueue('data: update\n\n');
    } catch (error) {
      console.error('Error sending update to client:', error);
      // Remove failed client
      clients.delete(client);
    }
  });
}

export async function GET() {
  try {
    let pingInterval: NodeJS.Timeout;
    let currentController: ReadableStreamDefaultController;
    
    const stream = new ReadableStream({
      start(controller) {
        currentController = controller;
        clients.add(controller);

        // Send initial connection message
        controller.enqueue('data: connected\n\n');

        // Handle client disconnection
        pingInterval = setInterval(() => {
          try {
            currentController.enqueue('data: ping\n\n');
          } catch (error) {
            console.error('Error sending ping:', error);
            clearInterval(pingInterval);
            clients.delete(currentController);
          }
        }, 30000); // Send ping every 30 seconds to keep connection alive
      },
      cancel() {
        if (pingInterval) {
          clearInterval(pingInterval);
        }
        clients.delete(currentController);
      },
    });

    return new NextResponse(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
      },
    });
  } catch (error) {
    console.error('Error setting up SSE:', error);
    return new NextResponse('Error setting up event stream', { status: 500 });
  }
} 