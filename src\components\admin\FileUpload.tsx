'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Loader2, Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { Alert, AlertDescription } from '@/components/ui/alert';
import type { MediaCategory } from '@/types';

interface FileUploadProps {
  onUploadComplete: (file: { url: string }) => void;
  category: MediaCategory;
  maxFiles?: number;
  acceptedFileTypes?: Record<string, string[]>;
}

export function FileUpload({ 
  onUploadComplete, 
  category,
  maxFiles = 1,
  acceptedFileTypes = { 'image/*': ['.png', '.jpg', '.jpeg', '.webp'] }
}: FileUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    try {
      setError(null);
      setUploading(true);
      setUploadProgress(0);

      const file = acceptedFiles[0];
      
      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        throw new Error('حجم الملف يجب أن يكون أقل من 10 ميجابايت');
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        throw new Error('نوع الملف غير مدعوم. الأنواع المدعومة هي: JPG, PNG, WEBP');
      }

      const formData = new FormData();
      formData.append('file', file);
      formData.append('category', category);
      formData.append('title', file.name.split('.')[0]); // Use filename as title
      formData.append('type', 'IMAGE');

      const response = await fetch('/api/media/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل رفع الملف');
      }

      const data = await response.json() as { url: string };
      
      if (!data.url) {
        throw new Error('لم يتم استلام رابط الملف من الخادم');
      }

      toast({
        title: "تم الرفع بنجاح",
        description: "تم رفع الصورة بنجاح",
      });

      onUploadComplete(data);
    } catch (error) {
      console.error('Upload error:', error);
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء رفع الملف';
      setError(errorMessage);
      toast({
        title: "خطأ في الرفع",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  }, [category, onUploadComplete, toast]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    maxFiles,
    accept: acceptedFileTypes,
    disabled: uploading,
    multiple: false,
  });

  return (
    <div className="w-full space-y-4">
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer
          transition-colors duration-200 ease-in-out
          ${isDragActive ? 'border-primary bg-primary/5' : 'border-border'}
          ${uploading ? 'pointer-events-none opacity-50' : 'hover:border-primary hover:bg-primary/5'}
        `}
      >
        <input {...getInputProps()} />
        <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        {isDragActive ? (
          <p className="text-lg">اسحب الملف هنا...</p>
        ) : (
          <div className="space-y-2">
            <p className="text-lg">اسحب الملف هنا، أو انقر للاختيار</p>
            <p className="text-sm text-muted-foreground">
              PNG, JPG, JPEG أو WEBP (الحد الأقصى: 10MB)
            </p>
          </div>
        )}
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {uploading && (
        <div className="space-y-2">
          <Progress value={uploadProgress} className="w-full" />
          <div className="flex items-center justify-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-sm">جاري الرفع...</span>
          </div>
        </div>
      )}
    </div>
  );
} 