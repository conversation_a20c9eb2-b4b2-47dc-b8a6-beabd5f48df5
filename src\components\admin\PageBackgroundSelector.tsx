'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Image as ImageIcon, Upload } from 'lucide-react';
import Image from 'next/image';
import { MediaCategory, type MediaImage } from '@/lib/types';
import { FileUpload } from '@/components/admin/FileUpload';

interface PageBackgroundSelectorProps {
  currentImage?: string;
  onSelect: (imageUrl: string) => void;
  title: string;
}

export function PageBackgroundSelector({ currentImage, onSelect, title }: PageBackgroundSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isUploadMode, setIsUploadMode] = useState(false);
  const [images, setImages] = useState<MediaImage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const fetchImages = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/media?type=IMAGE&category=PAGE_BACKGROUND');
      if (!response.ok) throw new Error('Failed to fetch images');
      const data = await response.json();
      setImages(data);
    } catch (error) {
      toast({
        title: "خطأ في تحميل الصور",
        description: "حدث خطأ أثناء تحميل الصور. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen && !isUploadMode) {
      fetchImages();
    }
  }, [isOpen, isUploadMode]);

  const handleSelect = (imageUrl: string) => {
    onSelect(imageUrl);
    setIsOpen(false);
    toast({
      title: "تم التحديث",
      description: "تم تحديث صورة الخلفية بنجاح.",
    });
  };

  const handleUploadComplete = async (uploadedFile: { url: string }) => {
    await handleSelect(uploadedFile.url);
    setIsUploadMode(false);
  };

  return (
    <Card className="p-6">
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">{title}</h3>
          <div className="flex gap-2">
            <Button onClick={() => { setIsOpen(true); setIsUploadMode(false); }}>
              <ImageIcon className="h-4 w-4 ml-2" />
              اختيار من المكتبة
            </Button>
            <Button onClick={() => { setIsOpen(true); setIsUploadMode(true); }}>
              <Upload className="h-4 w-4 ml-2" />
              رفع صورة جديدة
            </Button>
          </div>
        </div>

        {currentImage ? (
          <div className="relative aspect-video rounded-lg overflow-hidden border border-border">
            <Image
              src={currentImage}
              alt="صورة الخلفية الحالية"
              fill
              className="object-cover"
            />
          </div>
        ) : (
          <div className="aspect-video rounded-lg bg-muted flex items-center justify-center border border-dashed border-border">
            <div className="text-center text-muted-foreground">
              <ImageIcon className="h-12 w-12 mx-auto mb-2" />
              <p>لم يتم اختيار صورة</p>
            </div>
          </div>
        )}
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>
              {isUploadMode ? 'رفع صورة جديدة' : 'اختيار صورة الخلفية'}
            </DialogTitle>
          </DialogHeader>
          
          {isUploadMode ? (
            <FileUpload
              category={MediaCategory.PAGE_BACKGROUND}
              onUploadComplete={handleUploadComplete}
              maxFiles={1}
              acceptedFileTypes={{ 'image/*': ['.png', '.jpg', '.jpeg', '.webp'] }}
            />
          ) : isLoading ? (
            <div className="flex items-center justify-center p-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto p-4">
              {images.map((image) => (
                <button
                  key={image.id}
                  onClick={() => handleSelect(image.url)}
                  className="relative aspect-video rounded-lg overflow-hidden hover:ring-2 hover:ring-primary focus:ring-2 focus:ring-primary focus:outline-none transition-all group"
                >
                  <Image
                    src={image.url}
                    alt={image.title || 'صورة خلفية'}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                    <span className="text-white text-sm">اختيار الصورة</span>
                  </div>
                </button>
              ))}
              {images.length === 0 && (
                <div className="col-span-full text-center py-8 text-muted-foreground">
                  <p>لا توجد صور متاحة</p>
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => setIsUploadMode(true)}
                  >
                    <Upload className="h-4 w-4 ml-2" />
                    رفع صورة جديدة
                  </Button>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Card>
  );
} 