'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { RadioTower, PlusCircle, Trash2, Save, Loader2, LinkIcon, Info } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useForm, useFieldArray, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

const newsTickerItemSchema = z.object({
  id: z.string(),
  text: z.string().min(10, { message: "نص الخبر يجب أن يكون 10 أحرف على الأقل" }).max(200, { message: "نص الخبر يجب ألا يتجاوز 200 حرف" }),
  icon: z.string().optional().describe("اسم أيقونة من lucide-react (مثال: Gift, AlertTriangle)"),
  link: z.string().url({ message: "الرجاء إدخال رابط صحيح أو اتركه فارغًا" }).optional().or(z.literal('')),
});

const newsTickerEditorSchema = z.object({
  items: z.array(newsTickerItemSchema).min(1, {message: "يجب أن يكون هناك خبر واحد على الأقل في الشريط"}),
  speed: z.coerce.number().min(10, {message: "السرعة يجب أن تكون 10 على الأقل (أبطأ)"}).max(100, {message: "السرعة يجب أن تكون 100 على الأكثر (أسرع)"}),
});

type NewsTickerEditorFormData = z.infer<typeof newsTickerEditorSchema>;

export default function AdminNewsTickerPage() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);

  const { control, register, handleSubmit, reset, formState: { errors, isSubmitting } } = useForm<NewsTickerEditorFormData>({
    resolver: zodResolver(newsTickerEditorSchema),
    defaultValues: {
      items: [],
      speed: 30
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "items",
  });

  useEffect(() => {
    const fetchNewsTicker = async () => {
      try {
        const response = await fetch('/api/news-ticker');
        if (response.ok) {
          const data = await response.json();
          reset(data);
        }
      } catch (error) {
        console.error('Error fetching news ticker:', error);
        toast({
          title: "خطأ",
          description: "حدث خطأ أثناء تحميل بيانات شريط الأخبار",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchNewsTicker();
  }, [reset, toast]);

  const onSubmit = async (data: NewsTickerEditorFormData) => {
    try {
      const response = await fetch('/api/news-ticker', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to save news ticker');
      }

      toast({
        title: "تم الحفظ",
        description: "تم حفظ التغييرات بنجاح",
      });
    } catch (error) {
      console.error('Error saving news ticker:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء حفظ التغييرات",
        variant: "destructive"
      });
    }
  };

  const addNewItem = () => {
    append({
      id: Date.now().toString(),
      text: 'خبر جديد هنا...',
      icon: '',
      link: '',
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <RadioTower className="h-6 w-6 text-primary" />
            <CardTitle>إدارة شريط الأخبار المتحرك</CardTitle>
          </div>
          <CardDescription>
            قم بإضافة، تعديل، أو حذف عناصر شريط الأخبار، والتحكم في سرعته.
            <br />
            أدخل اسم أيقونة من <a href="https://lucide.dev/icons/" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Lucide Icons</a> للحقل المخصص.
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit(onSubmit)}>
          <CardContent className="space-y-6">
            <div>
              <Label htmlFor="speed">سرعة شريط الأخبار (10 الأبطأ - 100 الأسرع)</Label>
              <Input
                id="speed"
                type="number"
                {...register("speed")}
                placeholder="مثال: 30"
                className={`mt-1 ${errors.speed ? 'border-destructive' : ''}`}
              />
              {errors.speed && <p className="text-destructive text-sm mt-1">{errors.speed.message}</p>}
            </div>
            
            <h3 className="text-lg font-semibold border-t pt-4">عناصر شريط الأخبار</h3>
            {fields.map((field, index) => (
              <Card key={field.id} className="p-4 border shadow-sm space-y-4">
                <div className="flex justify-between items-start gap-4">
                  <div className="flex-1 space-y-4">
                    <div>
                      <Label htmlFor={`items.${index}.text`}>نص الخبر</Label>
                      <Input
                        {...register(`items.${index}.text`)}
                        placeholder="أدخل نص الخبر هنا..."
                        className={errors.items?.[index]?.text ? 'border-destructive' : ''}
                      />
                      {errors.items?.[index]?.text && (
                        <p className="text-destructive text-sm mt-1">{errors.items[index]?.text?.message}</p>
                      )}
                    </div>
                    <div>
                      <Label htmlFor={`items.${index}.link`} className="flex items-center gap-2">
                        <LinkIcon className="h-4 w-4" />
                        رابط (اختياري)
                      </Label>
                      <Input
                        {...register(`items.${index}.link`)}
                        placeholder="https://example.com"
                        className={errors.items?.[index]?.link ? 'border-destructive' : ''}
                      />
                      {errors.items?.[index]?.link && (
                        <p className="text-destructive text-sm mt-1">{errors.items[index]?.link?.message}</p>
                      )}
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="destructive"
                    size="icon"
                    onClick={() => remove(index)}
                    className="shrink-0"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </Card>
            ))}
            
            {errors.items && typeof errors.items.message === 'string' && (
              <p className="text-destructive text-sm mt-1">{errors.items.message}</p>
            )}
            
            <Button type="button" variant="outline" onClick={addNewItem} className="mt-4">
              <PlusCircle className="ml-2 h-4 w-4 rtl:mr-2" /> إضافة خبر جديد
            </Button>
          </CardContent>
          <CardFooter>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin rtl:mr-2" />
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <Save className="ml-2 h-4 w-4 rtl:mr-2" />
                  حفظ التغييرات
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
