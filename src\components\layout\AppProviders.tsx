"use client";

import { ThemeProvider as NextThemesProvider } from "next-themes";
import type { ThemeProviderProps } from "next-themes/dist/types";
import { useEffect, useState } from "react";
import { AuthProvider } from "@/components/auth/AuthProvider";

export function AppProviders({ children, ...props }: ThemeProviderProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // To prevent hydration mismatch, render children directly on the server
    // and on the first client render. ThemeProvider will kick in after mount.
    return <>{children}</>;
  }

  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme="light" // Set light as default, user can toggle
      enableSystem
      disableTransitionOnChange
      {...props}
    >
      <AuthProvider>{children}</AuthProvider>
    </NextThemesProvider>
  );
}
