/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'img.youtube.com',
      },
      {
        protocol: 'https',
        hostname: 'i.ytimg.com',
      },
      {
        protocol: 'https',
        hostname: 'media.elbalad.news',
      },
      {
        protocol: 'https',
        hostname: 'placehold.co',
      },
      {
        protocol: 'https',
        hostname: '**.cloudfront.net',
      },
      {
        protocol: 'https',
        hostname: '**.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: 'm.gomhuriaonline.com',
        pathname: '/Upload/**',
      },
      {
        protocol: 'https',
        hostname: 'www.gomhuriaonline.com',
        pathname: '/Upload/**',
      },
      {
        protocol: 'https',
        hostname: '**.cloudinary.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'res.cloudinary.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'newsroom.info',
        pathname: '/Upload/**',
      },
      {
        protocol: 'https',
        hostname: 'www.fakera.com',
      },
      {
        protocol: 'https',
        hostname: '**.githubusercontent.com',
      }
    ],
    domains: [
      'placehold.co',
      'gate.ahram.org.eg',
      'images.unsplash.com',
      'res.cloudinary.com'
    ],
  },
  transpilePackages: ['@prisma/client'],
  experimental: {
    serverActions: true,
    turbo: {
      rules: {
        "*.png": ["raw"],
        "*.jpg": ["raw"],
        "*.jpeg": ["raw"],
      },
      resolve: {
        fallback: {
          net: false,
          tls: false,
          fs: false,
          crypto: false,
          stream: false,
          timers: false,
          events: false,
        }
      }
    }
  },
}

module.exports = nextConfig