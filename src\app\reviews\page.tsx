'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Star } from 'lucide-react';
import { fetchComments } from '@/lib/api-utils';
import PageHeader from '@/components/layout/PageHeader';
import { ReviewsList } from '@/components/reviews/ReviewsList';

interface Comment {
  id: number;
  author: string;
  content: string;
  date: string;
  status: 'pending' | 'approved' | 'rejected';
}

const RatingStars = ({ rating = 5 }: { rating?: number }) => (
  <div className="flex">
    {[...Array(5)].map((_, i) => (
      <Star
        key={i}
        className={`h-5 w-5 ${i < rating ? 'text-yellow-400 fill-yellow-400' : 'text-muted-foreground'}`}
      />
    ))}
  </div>
);

export default function ReviewsPage() {
  const [comments, setComments] = useState<Comment[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadComments = async () => {
      try {
        const data = await fetchComments();
        // Only show approved comments
        setComments(data.filter((comment: Comment) => comment.status === 'approved'));
      } catch (error) {
        console.error('Error loading comments:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadComments();
    // تحديث التعليقات كل 30 ثانية
    const interval = setInterval(loadComments, 30 * 1000);
    return () => clearInterval(interval);
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-16 md:space-y-20 fade-in-up">
      <PageHeader
        title="آراء العملاء"
        subtitle="نفخر بثقة عملائنا وآرائهم الإيجابية في خدماتنا. اقرأ تجارب حجاجنا ومعتمرينا معنا."
        backgroundImage="https://placehold.co/1200x600.png?text=آراء+العملاء"
      />
      
      <section className="container mx-auto px-4">
        <ReviewsList />
      </section>
    </div>
  );
}
