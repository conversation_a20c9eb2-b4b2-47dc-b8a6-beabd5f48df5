import { NextResponse } from 'next/server';
import { loadData, saveData } from '@/lib/data-utils';

const FAQS_FILE = 'faqs.json';

export async function GET() {
  const faqs = await loadData(FAQS_FILE, []);
  return NextResponse.json(faqs);
}

export async function POST(req: Request) {
  const data = await req.json();
  const faqs = await loadData(FAQS_FILE, []);
  
  if (!data.id) {
    data.id = Date.now();
    faqs.push(data);
  } else {
    const index = faqs.findIndex((f: any) => f.id === data.id);
    if (index !== -1) {
      faqs[index] = data;
    }
  }
  
  await saveData(FAQS_FILE, faqs);
  return NextResponse.json(data);
}

export async function DELETE(req: Request) {
  const { id } = await req.json();
  const faqs = await loadData(FAQS_FILE, []);
  const newFaqs = faqs.filter((f: any) => f.id !== id);
  await saveData(FAQS_FILE, newFaqs);
  return NextResponse.json({ success: true });
} 