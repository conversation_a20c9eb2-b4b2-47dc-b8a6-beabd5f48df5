# نظام إدارة المحتوى

## الهيكل التقني

### قاعدة البيانات (MySQL)
- جداول متعددة لإدارة:
  - المستخدمين والصلاحيات
  - المحتوى العام
  - الأخبار
  - الأسئلة الشائعة
  - الوسائط (صور وفيديوهات)
  - التعليقات
  - شريط الأخبار
  - الإعدادات العامة

### المصادقة والتفويض
- نظام مصادقة JWT
- حماية مسارات لوحة التحكم
- إدارة جلسات المستخدمين
- صلاحيات خاصة للمدير

### واجهة المستخدم
- لوحة تحكم كاملة للمدير
- واجهة عربية سهلة الاستخدام
- تصميم متجاوب
- سمة داكنة/فاتحة
- تنبيهات وإشعارات

### أقسام لوحة التحكم
1. الصفحة الرئيسية
   - إحصائيات وملخصات
   - آخر التحديثات

2. إدارة المحتوى
   - محرر الصفحة الرئيسية
   - إدارة الصفحات
   - محسن المحتوى (AI)

3. الأخبار والإعلانات
   - إدارة الأخبار
   - شريط الأخبار المتحرك

4. الوسائط
   - مكتبة الصور
   - مكتبة الفيديوهات

5. التفاعل
   - إدارة التعليقات
   - الأسئلة الشائعة

6. المستخدمين والإعدادات
   - إدارة المستخدمين
   - الإعدادات العامة

## التقنيات المستخدمة
- Next.js مع TypeScript
- MySQL لقاعدة البيانات
- JWT للمصادقة
- TailwindCSS للتصميم
- Radix UI للمكونات

## الأمان
- حماية مسارات لوحة التحكم
- تشفير كلمات المرور
- معالجة الجلسات
- التحقق من الصلاحيات

## التطوير المستقبلي
1. إضافة المزيد من الوحدات:
   - نظام حجز المواعيد
   - إدارة الباقات والخدمات
   - التقارير والإحصائيات

2. تحسينات:
   - تحسين أداء الصفحات
   - إضافة المزيد من خيارات التخصيص
   - تكامل مع خدمات إضافية