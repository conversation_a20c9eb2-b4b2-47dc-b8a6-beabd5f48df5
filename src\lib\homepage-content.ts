export interface HeroContent {
  backgroundImageUrl: string;
  dataAiHint: string;
  title: string;
  subtitle: string;
}

export interface TripItem {
  id: number;
  imageUrl: string;
  dataAiHint: string;
  title: string;
  description: string;
}

export interface HomepageData {
  hero: {
    backgroundImageUrl: string;
    dataAiHint: string;
    title: string;
    subtitle: string;
  };
  latestTrips: Array<{
    id: number;
    imageUrl: string;
    dataAiHint: string;
    title: string;
    description: string;
  }>;
}

export const homepageContent: HomepageData = {
  hero: {
    backgroundImageUrl: 'https://placehold.co/1200x600.png?text=خلفية+الكعبة+المشرفة',
    dataAiHint: 'kaaba mecca',
    title: 'مرحباً بكم في إيثار لخدمات الحجاج',
    subtitle: 'نقدم لكم أفضل تجربة حج وعمرة مع خدمات متكاملة ورعاية فائقة لضمان راحتكم وأداء مناسككم بيسر وسهولة.',
  },
  latestTrips: [
    { 
      id: 1, 
      imageUrl: 'https://placehold.co/600x400.png?text=صورة+رحلة+مميزة', 
      dataAiHint: 'hajj travel', 
      title: 'رحلة حج 1445', 
      description: 'تجربة إيمانية مميزة لا تُنسى مع خدمات راقية.' 
    },
    { 
      id: 2, 
      imageUrl: 'https://placehold.co/600x400.png?text=صورة+عمرة+رجب', 
      dataAiHint: 'umrah group', 
      title: 'عمرة رجب الفضيل', 
      description: 'أجواء روحانية وسكينة في رحاب مكة والمدينة.' 
    },
    { 
      id: 3, 
      imageUrl: 'https://placehold.co/600x400.png?text=فندق+قريب+من+الحرم', 
      dataAiHint: 'mecca hotel', 
      title: 'باقة الإقامة المريحة', 
      description: 'فنادق مختارة بعناية بالقرب من الحرمين.' 
    },
    { 
      id: 4, 
      imageUrl: 'https://placehold.co/600x400.png?text=فريق+إيثار+المتفاني', 
      dataAiHint: 'customer service', 
      title: 'خدمة عملاء متميزة', 
      description: 'فريقنا في خدمتكم على مدار الساعة لضمان راحتكم.' 
    },
  ],
};
