import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { prisma } from '@/lib/prisma';
import { MediaCategory, MediaType } from '@prisma/client';
import { existsSync } from 'fs';

const isValidCategory = (category: string | null): category is MediaCategory => {
  return category !== null && Object.values(MediaCategory).includes(category as MediaCategory);
};

const isValidMediaType = (type: string | null): type is MediaType => {
  return type !== null && Object.values(MediaType).includes(type as MediaType);
};

export async function POST(request: NextRequest) {
  try {
    console.log('Starting file upload process...');
    
    const formData = await request.formData();
    const file = formData.get('file') as File | null;
    const title = formData.get('title') as string;
    const description = formData.get('description') as string;
    const categoryInput = formData.get('category');
    const typeInput = formData.get('type');
    const videoUrl = formData.get('videoUrl') as string;
    const thumbnailUrl = formData.get('thumbnailUrl') as string;
    const groupId = formData.get('groupId') as string;
    const groupTitle = formData.get('groupTitle') as string;

    console.log('Received form data:', {
      hasFile: !!file,
      title,
      category: categoryInput,
      type: typeInput
    });

    // Validate type
    const type = isValidMediaType(typeInput as string | null)
      ? typeInput as MediaType
      : MediaType.IMAGE;

    // Validate category
    const category = isValidCategory(categoryInput as string | null) 
      ? categoryInput as MediaCategory 
      : MediaCategory.OTHER;

    // Prepare base media data
    const baseMediaData = {
      type,
      title: title || 'Untitled',
      description: description || '',
      category,
    };

    // For videos, we only need the URL
    if (type === 'VIDEO') {
      if (!videoUrl) {
        return NextResponse.json(
          { error: 'Video URL is required' },
          { status: 400 }
        );
      }

      const media = await prisma.media.create({
        data: {
          ...baseMediaData,
          url: videoUrl,
          thumbnailUrl,
          ...(groupId && { groupId }),
          ...(groupTitle && { groupTitle }),
        },
      });

      return NextResponse.json(media);
    }

    // For images, we need the file
    if (!file) {
      console.error('No file provided in request');
      return NextResponse.json(
        { error: 'لم يتم تحديد ملف للرفع' },
        { status: 400 }
      );
    }

    // Validate file size (10MB limit)
    const MAX_SIZE = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > MAX_SIZE) {
      console.error('File size exceeds limit:', file.size);
      return NextResponse.json(
        { error: 'حجم الملف يجب أن يكون أقل من 10 ميجابايت' },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      console.error('Invalid file type:', file.type);
      return NextResponse.json(
        { error: 'نوع الملف غير مدعوم. الأنواع المدعومة هي: JPG, PNG, WEBP' },
        { status: 400 }
      );
    }

    try {
      // Create unique filename
      const bytes = await file.arrayBuffer();
      const buffer = Buffer.from(bytes);
      const timestamp = Date.now();
      const ext = file.name.split('.').pop()?.toLowerCase() || 'jpg';
      const filename = `${timestamp}-${Math.random().toString(36).substring(2)}.${ext}`;

      // Ensure uploads directory exists
      const uploadDir = join(process.cwd(), 'public/uploads');
      
      // Create uploads directory if it doesn't exist
      if (!existsSync(uploadDir)) {
        try {
          await mkdir(uploadDir, { recursive: true });
          console.log('Created uploads directory:', uploadDir);
        } catch (mkdirError) {
          console.error('Error creating uploads directory:', mkdirError);
          return NextResponse.json(
            { error: 'حدث خطأ أثناء إنشاء مجلد الرفع' },
            { status: 500 }
          );
        }
      }
      
      console.log('Saving file:', {
        filename,
        size: buffer.length,
        uploadDir
      });

      try {
        // Save file
        const path = join(uploadDir, filename);
        await writeFile(path, buffer);
        console.log('File saved successfully');
      } catch (writeError) {
        console.error('Error writing file:', writeError);
        return NextResponse.json(
          { error: 'حدث خطأ أثناء حفظ الملف على الخادم' },
          { status: 500 }
        );
      }

      try {
        // Save to database
        const media = await prisma.media.create({
          data: {
            ...baseMediaData,
            url: `/uploads/${filename}`,
            ...(groupId && { groupId }),
            ...(groupTitle && { groupTitle }),
          },
        });

        console.log('Database record created:', media.id);
        return NextResponse.json(media);
      } catch (dbError) {
        console.error('Error saving to database:', dbError);
        // Try to clean up the file if database save fails
        try {
          const path = join(uploadDir, filename);
          await unlink(path);
        } catch (unlinkError) {
          console.error('Error cleaning up file after failed db save:', unlinkError);
        }
        return NextResponse.json(
          { error: 'حدث خطأ أثناء حفظ معلومات الملف في قاعدة البيانات' },
          { status: 500 }
        );
      }
    } catch (error) {
      console.error('Error in file processing:', error);
      return NextResponse.json(
        { error: 'حدث خطأ أثناء معالجة الملف' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in upload handler:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء معالجة الطلب' },
      { status: 500 }
    );
  }
}

// Increase payload size limit for file uploads
export const config = {
  api: {
    bodyParser: false,
  },
}; 