import { PrismaClient } from '@prisma/client';
import { hash } from 'bcryptjs';

async function createInitialAdmin() {
  const prisma = new PrismaClient();
  const email = process.env.INITIAL_ADMIN_EMAIL;
  const password = process.env.INITIAL_ADMIN_PASSWORD;

  if (!email || !password) {
    console.error('Error: INITIAL_ADMIN_EMAIL and INITIAL_ADMIN_PASSWORD must be set in environment variables');
    process.exit(1);
  }

  try {
    // Check if admin exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      console.error('Admin user already exists:', email);
      process.exit(1);
    }

    // Hash password
    const hashedPassword = await hash(password, 10);

    // Create admin user
    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        role: 'ADMIN'
      }
    });

    console.log('Successfully created admin user:', email);
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

createInitialAdmin();