export async function fetchFaqs() {
  try {
    const response = await fetch('/api/faqs');
    if (!response.ok) throw new Error('Failed to fetch FAQs');
    return await response.json();
  } catch (error) {
    console.error('Error fetching FAQs:', error);
    return [];
  }
}

export async function fetchComments() {
  try {
    const response = await fetch('/api/comments', {
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to fetch comments');
    }

    const data = await response.json();
    if (!Array.isArray(data)) {
      throw new Error('Invalid data format received from server');
    }

    return data;
  } catch (error) {
    console.error('Error fetching comments:', error);
    throw error; // Let the component handle the error
  }
}

export async function fetchNews() {
  try {
    const response = await fetch('/api/news');
    if (!response.ok) throw new Error('Failed to fetch news');
    return await response.json();
  } catch (error) {
    console.error('Error fetching news:', error);
    return [];
  }
} 