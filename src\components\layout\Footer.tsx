import { Facebook, Twitter, Instagram, Youtube } from 'lucide-react';
import Link from 'next/link';
import { useSiteSettings } from '@/hooks/use-site-settings';

export default function Footer() {
  const currentYear = new Date().getFullYear();
  const { settings } = useSiteSettings();

  const socialLinks = [
    { name: 'Facebook', icon: Facebook, href: settings?.facebook || '#' },
    { name: 'Twitter', icon: Twitter, href: settings?.twitter || '#' },
    { name: 'Instagram', icon: Instagram, href: settings?.instagram || '#' },
    { name: 'Youtube', icon: Youtube, href: settings?.youtube || '#' },
  ];

  return (
    <footer className="bg-muted text-muted-foreground py-12 border-t">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-10 text-right md:text-start">
          <div>
            <h3 className="text-lg font-semibold text-foreground mb-4">{settings?.siteName || 'إيثار'}</h3>
            <p className="text-sm leading-relaxed">
              {settings?.siteDescription || 'نلتزم بتقديم أفضل الخدمات لضيوف الرحمن، ونسعى لجعل رحلتكم الإيمانية تجربة لا تُنسى.'}
            </p>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground mb-4">روابط سريعة</h3>
            <ul className="space-y-2">
              <li><Link href="/about" className="hover:text-primary transition-colors">من نحن</Link></li>
              <li><Link href="/services" className="hover:text-primary transition-colors">خدماتنا</Link></li>
              <li><Link href="/faq" className="hover:text-primary transition-colors">الأسئلة الشائعة</Link></li>
              <li><Link href="/contact" className="hover:text-primary transition-colors">تواصل معنا</Link></li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground mb-4">تواصل معنا</h3>
            <ul className="space-y-2">
              {settings?.phone && (
                <li>
                  <a href={`tel:${settings.phone}`} className="hover:text-primary transition-colors">
                    {settings.phone}
                  </a>
                </li>
              )}
              {settings?.email && (
                <li>
                  <a href={`mailto:${settings.email}`} className="hover:text-primary transition-colors">
                    {settings.email}
                  </a>
                </li>
              )}
              {settings?.address && (
                <li className="text-sm">{settings.address}</li>
              )}
              {settings?.workingHours && (
                <li className="text-sm">{settings.workingHours}</li>
              )}
            </ul>
          </div>
        </div>
        
        {/* Social Links */}
        <div className="flex justify-center space-x-4 rtl:space-x-reverse mb-8">
          {socialLinks.map((link) => {
            const Icon = link.icon;
            if (!link.href || link.href === '#') return null;
            return (
              <a
                key={link.name}
                href={link.href}
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-primary transition-colors"
                aria-label={link.name}
              >
                <Icon className="h-5 w-5" />
              </a>
            );
          })}
        </div>

        {/* Copyright */}
        <div className="text-center text-sm">
          <p>جميع الحقوق محفوظة &copy; {currentYear} {settings?.siteName || 'إيثار'}</p>
        </div>
      </div>
    </footer>
  );
}
