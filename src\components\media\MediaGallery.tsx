'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { ImageIcon as ImageIconLucide, PlayCircle, Loader2 } from 'lucide-react';
import { PhotoProvider, PhotoView } from 'react-photo-view';
import 'react-photo-view/dist/react-photo-view.css';

interface MediaItem {
  id: string;
  title: string;
  description?: string;
  url: string;
  thumbnailUrl?: string;
  type: 'IMAGE' | 'VIDEO';
  category: string;
  groupId?: string;
  groupTitle?: string;
  created_at: string;
}

interface MediaGroup {
  id: string;
  title: string;
  description?: string;
  category: string;
  type: 'IMAGE' | 'VIDEO';
  items: MediaItem[];
  thumbnailUrl?: string;
}

function getCategoryLabel(category: string): string {
  const labels: Record<string, string> = {
    'HAJJ': 'رحلات الحج',
    'UMRAH': 'رحلات العمرة',
    'HOTEL': 'الفنادق والإقامة',
    'EVENT': 'الفعاليات والمناسبات',
    'OTHER': 'أخرى'
  };
  return labels[category] || category;
}

function getVideoThumbnail(video: MediaItem): string {
  if (video.thumbnailUrl) return video.thumbnailUrl;
  if (video.url.includes('youtube.com') || video.url.includes('youtu.be')) {
    const videoId = video.url.includes('youtube.com') 
      ? video.url.split('v=')[1]?.split('&')[0]
      : video.url.split('/').pop();
    return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
  }
  return 'https://placehold.co/600x400.png?text=فيديو';
}

export function MediaGallery() {
  const [activeTab, setActiveTab] = useState<'images' | 'videos'>('images');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [mediaGroups, setMediaGroups] = useState<MediaGroup[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [videoDialogOpen, setVideoDialogOpen] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState<MediaItem | null>(null);

  useEffect(() => {
    fetchMedia();
  }, [activeTab, selectedCategory]);

  const fetchMedia = async () => {
    try {
      const type = activeTab === 'images' ? 'IMAGE' : 'VIDEO';
      const url = new URL('/api/media', window.location.origin);
      url.searchParams.append('type', type);
      if (selectedCategory !== 'all') {
        url.searchParams.append('category', selectedCategory);
      }

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Failed to fetch media');
      }
      const data = await response.json() as MediaItem[];

      // تجميع العناصر في مجموعات
      const groups = data.reduce((acc: MediaGroup[], item) => {
        // البحث عن مجموعة موجودة بنفس الـ groupId
        const existingGroup = acc.find(g => g.id === item.groupId);
        if (existingGroup) {
          existingGroup.items.push(item);
          return acc;
        }

        // إنشاء مجموعة جديدة
        const newGroup: MediaGroup = {
          id: item.groupId || item.id,
          title: item.groupTitle || item.title,
          description: item.description || undefined,
          category: item.category,
          type: item.type,
          items: [item],
          thumbnailUrl: item.thumbnailUrl || undefined
        };
        acc.push(newGroup);
        return acc;
      }, []);

      // ترتيب العناصر داخل كل مجموعة حسب الرقم في العنوان
      groups.forEach(group => {
        group.items.sort((a, b) => {
          const aNum = parseInt(a.title.match(/\d+$/)?.[0] || '0');
          const bNum = parseInt(b.title.match(/\d+$/)?.[0] || '0');
          return aNum - bNum;
        });
      });

      // ترتيب المجموعات حسب تاريخ آخر عنصر في كل مجموعة
      const sortedGroups = groups.sort((a, b) => {
        const aDate = new Date(a.items[0].created_at);
        const bDate = new Date(b.items[0].created_at);
        return bDate.getTime() - aDate.getTime();
      });

      setMediaGroups(sortedGroups);
    } catch (error) {
      console.error('Error fetching media:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleVideoClick = (video: MediaItem) => {
    setSelectedVideo(video);
    setVideoDialogOpen(true);
  };

  function getVideoEmbedUrl(url: string): string {
    if (url.includes('youtube.com') || url.includes('youtu.be')) {
      const videoId = url.includes('youtube.com') 
        ? url.split('v=')[1]?.split('&')[0]
        : url.split('/').pop();
      return `https://www.youtube.com/embed/${videoId}`;
    }
    return url;
  }

  const categories = ['HAJJ', 'UMRAH', 'HOTEL', 'EVENT', 'OTHER'];

  return (
    <div className="space-y-8">
      {/* Filters */}
      <Card className="p-6 shadow-md">
        <div className="grid sm:grid-cols-2 gap-4">
          <div>
            <label htmlFor="category-filter" className="block text-sm font-medium text-muted-foreground mb-1">تصفية حسب الفئة</label>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger id="category-filter">
                <SelectValue placeholder="اختر الفئة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">كل الفئات</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>
                    {getCategoryLabel(category)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </Card>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'images' | 'videos')} className="w-full" dir="rtl">
        <TabsList className="grid w-full grid-cols-2 md:w-1/2 mx-auto">
          <TabsTrigger value="images" className="flex items-center gap-2">
            <ImageIconLucide className="h-5 w-5" /> الصور
          </TabsTrigger>
          <TabsTrigger value="videos" className="flex items-center gap-2">
            <PlayCircle className="h-5 w-5" /> الفيديوهات
          </TabsTrigger>
        </TabsList>
        <TabsContent value="images">
          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mt-8">
              {mediaGroups.filter(group => group.type === 'IMAGE').map((group) => (
                <PhotoProvider
                  key={group.id}
                  maskOpacity={0.8}
                  speed={() => 300}
                  easing={(type) => (type === 2 ? 'cubic-bezier(0.36, 0, 0.66, -0.56)' : 'cubic-bezier(0.34, 1.56, 0.64, 1)')}
                >
                  <Card className="overflow-hidden group hover:shadow-lg transition-shadow duration-300">
                    <PhotoView src={group.items[0].url}>
                      <div className="relative aspect-video cursor-pointer">
                        <Image
                          src={group.items[0].url}
                          alt={group.title}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
                        />
                        {group.items.length > 1 && (
                          <div className="absolute bottom-2 right-2 bg-background/80 px-2 py-1 rounded text-sm">
                            <ImageIconLucide className="h-4 w-4 inline-block ml-1" />
                            {group.items.length}
                          </div>
                        )}
                      </div>
                    </PhotoView>
                    <CardContent className="p-4">
                      <h3 className="font-semibold text-md truncate" title={group.title}>{group.title}</h3>
                      <p className="text-xs text-muted-foreground">{getCategoryLabel(group.category)}</p>
                      {group.description && (
                        <p className="text-sm text-muted-foreground mt-2 line-clamp-2">{group.description}</p>
                      )}
                      {group.items.length > 1 && (
                        <div className="mt-4 flex flex-wrap gap-2">
                          {group.items.map((item, index) => (
                            <PhotoView key={item.id} src={item.url}>
                              <div className="w-12 h-12 relative rounded overflow-hidden cursor-pointer">
                                <Image
                                  src={item.url}
                                  alt={`${group.title} - ${index + 1}`}
                                  fill
                                  className="object-cover"
                                />
                              </div>
                            </PhotoView>
                          ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </PhotoProvider>
              ))}
            </div>
          )}
        </TabsContent>
        <TabsContent value="videos">
          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mt-8">
              {mediaGroups.filter(group => group.type === 'VIDEO').map((group) => (
                <Card 
                  key={group.id} 
                  className="overflow-hidden group cursor-pointer hover:shadow-lg transition-shadow duration-300"
                  onClick={() => handleVideoClick(group.items[0])}
                >
                  <div className="relative aspect-video">
                    <Image
                      src={getVideoThumbnail(group.items[0])}
                      alt={group.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                      sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
                    />
                    <div className="absolute inset-0 flex items-center justify-center bg-black/40">
                      <PlayCircle className="h-16 w-16 text-white opacity-80 group-hover:opacity-100 transition-opacity" />
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-semibold text-md truncate" title={group.title}>{group.title}</h3>
                    <p className="text-xs text-muted-foreground">{getCategoryLabel(group.category)}</p>
                    {group.description && (
                      <p className="text-sm text-muted-foreground mt-2 line-clamp-2">{group.description}</p>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {!isLoading && mediaGroups.length === 0 && (
        <p className="text-center text-lg text-muted-foreground py-10">
          لا توجد عناصر تطابق معايير البحث الحالية.
        </p>
      )}

      {/* Video Dialog */}
      <Dialog open={videoDialogOpen} onOpenChange={setVideoDialogOpen}>
        <DialogContent className="max-w-5xl w-full p-0">
          <DialogTitle className="sr-only">
            {selectedVideo?.title || 'عرض الفيديو'}
          </DialogTitle>
          <div className="aspect-video w-full">
            {selectedVideo && (
              <iframe
                src={getVideoEmbedUrl(selectedVideo.url)}
                title={selectedVideo.title}
                className="w-full h-full"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              />
            )}
          </div>
          {selectedVideo && (selectedVideo.title || selectedVideo.description) && (
            <div className="p-6 bg-background">
              <h3 className="text-lg font-semibold">{selectedVideo.title}</h3>
              {selectedVideo.description && (
                <p className="text-muted-foreground mt-2">{selectedVideo.description}</p>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
} 