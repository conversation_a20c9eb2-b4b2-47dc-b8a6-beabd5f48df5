// This is an autogenerated file from Firebase Studio.
'use server';
/**
 * @fileOverview An AI tool to analyze website interactions, assess user intent,
 * and provide suggestions for improving content.
 *
 * - improveWebsiteContent - A function that handles the content improvement process.
 * - ImproveWebsiteContentInput - The input type for the improveWebsiteContent function.
 * - ImproveWebsiteContentOutput - The return type for the improveWebsiteContent function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const ImproveWebsiteContentInputSchema = z.object({
  websiteContent: z.string().describe('The current content of the website.'),
  userInteractions: z
    .string()
    .describe(
      'A description of user interactions on the website, including pages visited, time spent, and actions taken.'
    ),
  userIntent: z.string().describe('An assessment of user intent based on their interactions.'),
});
export type ImproveWebsiteContentInput = z.infer<typeof ImproveWebsiteContentInputSchema>;

const ImproveWebsiteContentOutputSchema = z.object({
  suggestions: z
    .string()
    .describe('Suggestions for improving the website content to better meet user needs and increase engagement.'),
  reasoning: z
    .string()
    .describe('Explanation of why the suggestions were made, referencing user interactions and intent.'),
});
export type ImproveWebsiteContentOutput = z.infer<typeof ImproveWebsiteContentOutputSchema>;

export async function improveWebsiteContent(input: ImproveWebsiteContentInput): Promise<ImproveWebsiteContentOutput> {
  return improveWebsiteContentFlow(input);
}

const prompt = ai.definePrompt({
  name: 'improveWebsiteContentPrompt',
  input: {schema: ImproveWebsiteContentInputSchema},
  output: {schema: ImproveWebsiteContentOutputSchema},
  prompt: `You are an AI assistant specialized in providing content improvement suggestions for websites.

You will receive information about the current website content, user interactions, and an assessment of user intent.
Based on this information, you will provide specific and actionable suggestions for improving the website content to better meet user needs and increase engagement.

Website Content:
{{{websiteContent}}}

User Interactions:
{{{userInteractions}}}

User Intent:
{{{userIntent}}}

Suggestions:
{{suggestions}}

Reasoning:
{{reasoning}}`,
});

const improveWebsiteContentFlow = ai.defineFlow(
  {
    name: 'improveWebsiteContentFlow',
    inputSchema: ImproveWebsiteContentInputSchema,
    outputSchema: ImproveWebsiteContentOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
