'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { MapPin, Phone, Mail, Send, Clock, Loader2, Facebook, Twitter, Instagram, Youtube, Globe } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import PageHeader from '@/components/layout/PageHeader';
import { ContactForm } from '@/components/contact/ContactForm';

interface ContactInfo {
  id: string;
  phone: string;
  email: string;
  address: string;
  workingHours: string;
  whatsapp: string;
  facebook?: string;
  twitter?: string;
  instagram?: string;
  youtube?: string;
  website?: string;
  mapLocation: {
    lat: number;
    lng: number;
    zoom: number;
  };
}

interface PageContent {
  backgroundImage?: string;
}

const contactInfoData = [
  {
    icon: Phone,
    title: 'اتصل بنا',
    content: '+966 12 345 6789'
  },
  {
    icon: Mail,
    title: 'البريد الإلكتروني',
    content: '<EMAIL>'
  },
  {
    icon: MapPin,
    title: 'العنوان',
    content: 'المملكة العربية السعودية، مكة المكرمة'
  },
  {
    icon: Clock,
    title: 'ساعات العمل',
    content: 'السبت - الخميس: 9:00 صباحاً - 5:00 مساءً'
  }
];

export default function ContactPage() {
  const [contactInfo, setContactInfo] = useState<ContactInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    message: ''
  });
  const { toast } = useToast();
  const googleMapsApiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
  const [pageContent, setPageContent] = useState<PageContent>({});

  const loadContactInfo = async () => {
    try {
      const response = await fetch('/api/settings', {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
      if (!response.ok) throw new Error('Failed to fetch contact info');
      const data = await response.json();
      setContactInfo(data);
    } catch (error) {
      console.error('Error loading contact info:', error);
      toast({
        title: "خطأ في تحميل معلومات الاتصال",
        description: "حدث خطأ أثناء تحميل معلومات الاتصال. يرجى تحديث الصفحة.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadContactInfo();

    // إعداد SSE للتحديثات المباشرة
    const eventSource = new EventSource('/api/updates');

    eventSource.onmessage = (event) => {
      if (event.data === 'update') {
        loadContactInfo();
      }
    };

    eventSource.onerror = (error) => {
      console.error('SSE Error:', error);
      eventSource.close();
    };

    return () => {
      eventSource.close();
    };
  }, []);

  useEffect(() => {
    const fetchPageContent = async () => {
      try {
        const response = await fetch('/api/pages');
        if (response.ok) {
          const data = await response.json();
          setPageContent(data.contact || {});
        }
      } catch (error) {
        console.error('Error fetching page content:', error);
      }
    };

    fetchPageContent();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) throw new Error('Failed to send message');

      toast({
        title: "تم إرسال الرسالة بنجاح",
        description: "سنقوم بالرد عليك في أقرب وقت ممكن.",
      });

      setFormData({
        name: '',
        email: '',
        phone: '',
        message: ''
      });
    } catch (error) {
      toast({
        title: "خطأ في إرسال الرسالة",
        description: "حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-16 md:space-y-20 fade-in-up">
      <PageHeader
        title="تواصل معنا"
        subtitle="نحن هنا لخدمتكم والإجابة على جميع استفساراتكم. يمكنكم التواصل معنا عبر أي من القنوات التالية."
        backgroundImage={pageContent.backgroundImage}
      />
      
      <section className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 gap-10">
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-primary">معلومات الاتصال</h2>
            <div className="grid gap-6">
              {contactInfoData.map((info, index) => {
                const Icon = info.icon;
                return (
                  <Card key={index} className="border-primary/10">
                    <CardContent className="flex items-start p-4 gap-4">
                      <div className="bg-primary/10 p-2 rounded-lg">
                        <Icon className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-foreground">{info.title}</h3>
                        <p className="text-muted-foreground">{info.content}</p>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-primary">أرسل رسالة</h2>
            <Card className="border-primary/10">
              <CardContent className="p-6">
                <ContactForm />
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
}
