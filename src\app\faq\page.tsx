'use client';

import { useState, useEffect } from 'react';
import PageHeader from '@/components/layout/PageHeader';
import { Card } from '@/components/ui/card';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

interface PageContent {
  backgroundImage?: string;
}

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category?: string;
}

export default function FAQPage() {
  const [pageContent, setPageContent] = useState<PageContent>({});
  const [faqs, setFaqs] = useState<FAQ[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch page content
        const pageResponse = await fetch('/api/pages');
        if (pageResponse.ok) {
          const pageData = await pageResponse.json();
          setPageContent(pageData.faq || {});
        }

        // Fetch FAQs
        const faqResponse = await fetch('/api/faqs');
        if (faqResponse.ok) {
          const faqData = await faqResponse.json();
          setFaqs(faqData);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="space-y-16 md:space-y-20 fade-in-up">
      <PageHeader
        title="الأسئلة الشائعة"
        subtitle="إجابات على الأسئلة المتكررة حول خدمات الحج والعمرة التي نقدمها."
        backgroundImage={pageContent.backgroundImage}
      />
      
      <section className="container mx-auto px-4">
        <Card className="p-6 md:p-8">
          <Accordion type="single" collapsible className="w-full space-y-4">
            {faqs.map((faq) => (
              <AccordionItem key={faq.id} value={faq.id} className="border rounded-lg px-4">
                <AccordionTrigger className="text-lg font-semibold text-primary hover:text-primary/90 py-4">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-foreground/90 leading-relaxed pb-4">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </Card>
      </section>

      <section className="text-center py-10">
        <h2 className="text-2xl font-semibold text-foreground mb-4">هل لديك سؤال آخر؟</h2>
        <p className="text-muted-foreground mb-6">
          إذا لم تجد إجابة لسؤالك، فلا تتردد في التواصل معنا مباشرة.
        </p>
        <a href="/contact" className="text-primary hover:underline">
          تواصل معنا
        </a>
      </section>
    </div>
  );
}
