generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id         String   @id @default(uuid())
  email      String   @unique
  password   String
  role       Role     @default(USER)
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  news      News[]
  comments  Comment[]

  @@map("users")
}

model Content {
  id         String      @id @default(uuid())
  type       ContentType
  title      String
  content    String      @db.Text
  created_at DateTime    @default(now())
  updated_at DateTime    @updatedAt

  @@unique([type, title], name: "type_title")
  @@map("content")
}

model News {
  id           String    @id @default(uuid())
  title        String
  content      String    @db.Text
  author_id    String
  author       User      @relation(fields: [author_id], references: [id])
  publish_date DateTime
  created_at   DateTime  @default(now())
  updated_at   DateTime  @updatedAt

  @@map("news")
}

model FAQ {
  id         String   @id @default(uuid())
  question   String   @db.Text
  answer     String   @db.Text
  category   String?
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@map("faqs")
}

model Media {
  id          String        @id @default(uuid())
  type        MediaType
  title       String
  url         String        @db.Text
  description String?       @db.Text
  thumbnailUrl String?      @db.Text
  category    MediaCategory @default(OTHER)
  groupId     String?
  groupTitle  String?
  created_at  DateTime      @default(now())
  updated_at  DateTime      @updatedAt

  @@map("media")
}

model Comment {
  id         String        @id @default(uuid())
  content    String        @db.Text
  user_id    String
  user       User          @relation(fields: [user_id], references: [id])
  status     CommentStatus @default(PENDING)
  created_at DateTime      @default(now())
  updated_at DateTime      @updatedAt

  @@map("comments")
}

model NewsTicker {
  id         String   @id @default(uuid())
  text       String   @db.Text
  link       String?
  active     Boolean  @default(true)
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@map("news_ticker")
}

model Setting {
  id         String      @id @default(uuid())
  name       String      @unique
  value      String?     @db.Text
  type       String      @default("TEXT")
  group      String      @default("GENERAL")
  created_at DateTime    @default(now())
  updated_at DateTime    @updatedAt

  @@map("settings")
}

enum Role {
  ADMIN
  USER
}

enum ContentType {
  HOMEPAGE
  ABOUT
  SERVICES
  CONTACT
  PAGE
}

enum MediaType {
  IMAGE
  VIDEO
}

enum MediaCategory {
  PREVIOUS_TRIP
  GALLERY
  NEWS
  PAGE_BACKGROUND
  OTHER
}

enum CommentStatus {
  PENDING
  APPROVED
  REJECTED
}