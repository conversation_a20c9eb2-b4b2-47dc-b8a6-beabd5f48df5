import { NextResponse } from 'next/server';
import { writeFile, readFile, mkdir } from 'fs/promises';
import { join } from 'path';
import type { Package } from '@/app/admin/pages/components/PackageEditor';
import type { AdditionalService } from '@/app/admin/pages/components/AdditionalServicesEditor';

interface ServiceContent {
  hajjPackages: Package[];
  umrahPackages: Package[];
  additionalServices: AdditionalService[];
  pageHeader?: {
    backgroundImage: string;
  };
}

import { prisma } from '@/lib/prisma';

export async function POST(request: Request) {
  try {
    const content: ServiceContent = await request.json();
    
    // Update page header in database if provided
    if (content.pageHeader) {
      const existingContent = await prisma.content.findFirst({
        where: { type: 'SERVICES' }
      });

      if (existingContent) {
        await prisma.content.update({
          where: { id: existingContent.id },
          data: {
            content: JSON.stringify({
              ...JSON.parse(existingContent.content),
              backgroundImage: content.pageHeader.backgroundImage
            })
          }
        });
      } else {
        await prisma.content.create({
          data: {
            type: 'SERVICES',
            title: 'خدماتنا',
            content: JSON.stringify({
              backgroundImage: content.pageHeader.backgroundImage
            })
          }
        });
      }
    }
    
    // Save packages and services to JSON file
    const dataPath = join(process.cwd(), 'public', 'data');
    const filePath = join(dataPath, 'services.json');
    
    // Create data directory if it doesn't exist
    try {
      await mkdir(dataPath, { recursive: true });
      await writeFile(filePath, JSON.stringify(content, null, 2));
    } catch (error) {
      console.error('Error writing file:', error);
      throw error;
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error saving services:', error);
    return NextResponse.json({ error: 'Error saving services' }, { status: 500 });
  }
}

export async function GET() {
  try {
    // Get page header from database
    const pageContent = await prisma.content.findFirst({
      where: { type: 'SERVICES' }
    });
    
    // Get packages and services from JSON file
    const filePath = join(process.cwd(), 'public', 'data', 'services.json');
    const defaultData = {
      hajjPackages: [],
      umrahPackages: [],
      additionalServices: [],
      pageHeader: {
        backgroundImage: 'https://placehold.co/1200x600.png?text=خدماتنا+المتنوعة'
      }
    };

    try {
      const fileContent = await readFile(filePath, 'utf-8');
      const servicesData = JSON.parse(fileContent);
      
      // Merge with page header data
      return NextResponse.json({
        ...servicesData,
        pageHeader: pageContent ? {
          backgroundImage: JSON.parse(pageContent.content).backgroundImage
        } : defaultData.pageHeader
      });
    } catch (error) {
      console.log('No existing services file found, returning default data');
      return NextResponse.json(defaultData);
    }
  } catch (error) {
    console.error('Error reading services:', error);
    return NextResponse.json({ error: 'Error reading services' }, { status: 500 });
  }
} 