'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { <PERSON><PERSON>ir<PERSON>, PlusCircle, Edit3, Trash2, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useForm, Controller, type SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

const faqSchema = z.object({
  question: z.string().min(10, { message: "السؤال يجب أن يكون 10 أحرف على الأقل" }),
  answer: z.string().min(10, { message: "الإجابة يجب أن تكون 10 أحرف على الأقل" }),
});

type FAQEntry = z.infer<typeof faqSchema> & {
  id: number;
};

export default function AdminFaqsPage() {
  const [faqs, setFaqs] = useState<FAQEntry[]>([]);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingFaq, setEditingFaq] = useState<FAQEntry | null>(null);
  const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false);
  const [deletingFaqId, setDeletingFaqId] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  const { register, handleSubmit, control, reset, setValue, formState: { errors, isSubmitting } } = useForm<z.infer<typeof faqSchema>>({
    resolver: zodResolver(faqSchema),
    defaultValues: { question: '', answer: '' },
  });

  useEffect(() => {
    const loadFaqs = async () => {
      try {
        const response = await fetch('/api/faqs');
        if (!response.ok) throw new Error('Failed to fetch FAQs');
        const data = await response.json();
        setFaqs(data);
      } catch (error) {
        console.error('Error loading FAQs:', error);
        toast({
          title: "خطأ",
          description: "حدث خطأ أثناء تحميل الأسئلة الشائعة",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadFaqs();
    // تحديث البيانات كل 30 ثانية
    const interval = setInterval(loadFaqs, 30 * 1000);
    return () => clearInterval(interval);
  }, [toast]);

  const handleAddNew = () => {
    reset({ question: '', answer: '' });
    setEditingFaq(null);
    setIsAddModalOpen(true);
  };

  const handleEdit = (faq: FAQEntry) => {
    setEditingFaq(faq);
    setValue('question', faq.question);
    setValue('answer', faq.answer);
    setIsEditModalOpen(true);
  };

  const handleDelete = (id: number) => {
    setDeletingFaqId(id);
    setIsDeleteAlertOpen(true);
  };

  const confirmDelete = async () => {
    if (deletingFaqId !== null) {
      try {
        const response = await fetch('/api/faqs', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ id: deletingFaqId }),
        });

        if (response.ok) {
          setFaqs(faqs.filter(faq => faq.id !== deletingFaqId));
          toast({ title: "تم الحذف", description: "تم حذف السؤال الشائع بنجاح." });
        } else {
          throw new Error('Failed to delete FAQ');
        }
      } catch (error) {
        console.error('Error deleting FAQ:', error);
        toast({
          title: "خطأ",
          description: "حدث خطأ أثناء حذف السؤال الشائع",
          variant: "destructive",
        });
      }
      setDeletingFaqId(null);
    }
    setIsDeleteAlertOpen(false);
  };

  const onSubmit: SubmitHandler<z.infer<typeof faqSchema>> = async (data) => {
    try {
      const payload = editingFaq ? { ...data, id: editingFaq.id } : { ...data };
      const response = await fetch('/api/faqs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        const savedFaq = await response.json();
        if (editingFaq) {
          setFaqs(faqs.map(faq => faq.id === editingFaq.id ? savedFaq : faq));
          toast({ title: "تم التعديل", description: "تم تعديل السؤال الشائع بنجاح." });
          setIsEditModalOpen(false);
        } else {
          setFaqs([savedFaq, ...faqs]);
          toast({ title: "تمت الإضافة", description: "تمت إضافة السؤال الشائع بنجاح." });
          setIsAddModalOpen(false);
        }
      } else {
        throw new Error('Failed to save FAQ');
      }
    } catch (error) {
      console.error('Error saving FAQ:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء حفظ السؤال الشائع",
        variant: "destructive",
      });
    }
    reset();
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <HelpCircle className="h-6 w-6 text-primary" />
            <CardTitle>إدارة الأسئلة الشائعة</CardTitle>
          </div>
          <Button onClick={handleAddNew}>
            <PlusCircle className="ml-2 h-4 w-4" />
            إضافة سؤال جديد
          </Button>
        </CardHeader>
        <CardContent>
          {faqs.length > 0 ? (
            <Accordion type="single" collapsible className="w-full">
              {faqs.map((faq) => (
                <AccordionItem key={faq.id} value={`item-${faq.id}`}>
                  <div className="flex items-center justify-between pr-4">
                    <AccordionTrigger className="flex-1 text-right">{faq.question}</AccordionTrigger>
                    <div className="space-x-1 rtl:space-x-reverse">
                      <Button variant="ghost" size="icon" onClick={() => handleEdit(faq)}>
                        <Edit3 className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => handleDelete(faq.id)}>
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </div>
                  </div>
                  <AccordionContent className="pr-4">{faq.answer}</AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          ) : (
            <p className="text-center text-muted-foreground py-8">لا توجد أسئلة شائعة لعرضها.</p>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Modal */}
      <Dialog open={isAddModalOpen || isEditModalOpen} onOpenChange={editingFaq ? setIsEditModalOpen : setIsAddModalOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <form onSubmit={handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>{editingFaq ? 'تعديل السؤال الشائع' : 'إضافة سؤال شائع جديد'}</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div>
                <Label htmlFor="question">السؤال</Label>
                <Textarea id="question" {...register("question")} rows={3} className={`mt-1 ${errors.question ? 'border-destructive' : ''}`} />
                 {errors.question && <p className="text-destructive text-sm mt-1">{errors.question.message}</p>}
              </div>
              <div>
                <Label htmlFor="answer">الإجابة</Label>
                <Textarea id="answer" {...register("answer")} rows={5} className={`mt-1 ${errors.answer ? 'border-destructive' : ''}`} />
                {errors.answer && <p className="text-destructive text-sm mt-1">{errors.answer.message}</p>}
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => editingFaq ? setIsEditModalOpen(false) : setIsAddModalOpen(false)}>إلغاء</Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="ml-2 h-4 w-4 animate-spin rtl:mr-2" />}
                {editingFaq ? 'حفظ التعديلات' : 'إضافة السؤال'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteAlertOpen} onOpenChange={setIsDeleteAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد؟</AlertDialogTitle>
            <AlertDialogDescription>
              سيتم حذف هذا السؤال وإجابته بشكل دائم. لا يمكن التراجع عن هذا الإجراء.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete}>نعم، قم بالحذف</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

    