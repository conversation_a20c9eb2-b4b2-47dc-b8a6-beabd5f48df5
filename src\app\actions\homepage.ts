'use server';

import { prisma } from '@/lib/prisma';
import { revalidatePath } from 'next/cache';

interface UpdateHomepageData {
  title: string;
  subtitle: string;
  backgroundImageUrl: string;
  backgroundOpacity: number;
  titleColor: string;
  subtitleColor: string;
  latestTrips: {
    id: number;
    imageUrl: string;
    dataAiHint: string;
    title: string;
    description: string;
  }[];
}

export async function updateHomepage(data: UpdateHomepageData) {
  try {
    // First try to find existing content
    const existingContent = await prisma.content.findFirst({
      where: {
        type: 'HOMEPAGE'
      }
    });

    console.log('Updating homepage with data:', data); // Debug log

    // Parse existing content or create empty object if none exists
    const existingContentData = existingContent?.content 
      ? JSON.parse(existingContent.content)
      : {};

    // Update homepage content
    const updatedContent = existingContent
      ? await prisma.content.update({
          where: {
            id: existingContent.id
          },
          data: {
            title: data.title,
            content: JSON.stringify({
              ...existingContentData,
              subtitle: data.subtitle,
              backgroundImageUrl: data.backgroundImageUrl,
              backgroundOpacity: data.backgroundOpacity,
              titleColor: data.titleColor,
              subtitleColor: data.subtitleColor,
              latestTrips: data.latestTrips
            })
          }
        })
      : await prisma.content.create({
          data: {
            type: 'HOMEPAGE',
            title: data.title,
            content: JSON.stringify({
              subtitle: data.subtitle,
              backgroundImageUrl: data.backgroundImageUrl,
              backgroundOpacity: data.backgroundOpacity,
              titleColor: data.titleColor,
              subtitleColor: data.subtitleColor,
              latestTrips: data.latestTrips
            })
          }
        });

    // Revalidate the homepage to show new content immediately
    // Force immediate revalidation of both the homepage and admin pages
    revalidatePath('/', 'layout');
    revalidatePath('/admin/homepage-editor', 'page');
    revalidatePath('/(.*)', 'layout'); // Revalidate all routes
    
    console.log('Homepage updated successfully with data:', updatedContent); // Debug log

    return { success: true, data: updatedContent };
  } catch (error) {
    console.error('Error updating homepage:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to update homepage content' 
    };
  }
} 