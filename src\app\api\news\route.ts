import { NextResponse } from 'next/server';
import { loadData, saveData } from '@/lib/data-utils';
import { sendUpdateToAll } from '../updates/route';

interface NewsItem {
  id: number;
  title: string;
  content: string;
  author: string;
  date: string;
  imageUrl?: string;
}

const NEWS_FILE = 'news.json';

// This is a temporary solution. In a real application, this data would come from a database
const newsItems = [
  {
    id: 1,
    title: 'إطلاق باقة الحج الجديدة لموسم 1446',
    content: 'يسر شركة إيثار أن تعلن عن إطلاق باقة الحج الجديدة لموسم 1446، وتشمل خدمات مميزة تلبي احتياجات ضيوف الرحمن. تم تصميم الباقة بعناية لتوفير تجربة روحانية متكاملة مع أعلى معايير الراحة والخدمة.',
    author: 'فريق إيثار',
    date: '2024-05-15',
    imageUrl: 'https://placehold.co/600x400.png?text=باقة+حج+جديدة'
  },
  {
    id: 2,
    title: 'تحديثات هامة بخصوص تصاريح العمرة',
    content: 'نود إعلام عملائنا الكرام بآخر التحديثات المتعلقة بتصاريح العمرة والإجراءات الجديدة. تشمل التحديثات تسهيلات في إجراءات الحصول على التصاريح وتحسينات في نظام الحجز الإلكتروني.',
    author: 'قسم العمليات',
    date: '2024-05-10',
    imageUrl: 'https://placehold.co/600x400.png?text=تصاريح+عمرة'
  },
  {
    id: 3,
    title: 'افتتاح فرع جديد في المدينة المنورة',
    content: 'يسعدنا الإعلان عن افتتاح فرعنا الجديد في المدينة المنورة لخدمة ضيوف الرحمن بشكل أفضل. يقع الفرع في موقع استراتيجي قريب من المسجد النبوي الشريف.',
    author: 'إدارة التطوير',
    date: '2024-05-01',
    imageUrl: 'https://placehold.co/600x400.png?text=فرع+جديد'
  }
];

export async function GET() {
  try {
    const news = await loadData<NewsItem[]>(NEWS_FILE, []);
    return NextResponse.json(news);
  } catch (error) {
    console.error('Error fetching news:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب الأخبار' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const data = await request.json() as Partial<NewsItem>;
    const news = await loadData<NewsItem[]>(NEWS_FILE, []);
    
    if (!data.id) {
      // Add new news item
      const newItem: NewsItem = {
        id: Date.now(),
        title: data.title || '',
        content: data.content || '',
        author: data.author || '',
        date: new Date().toISOString().split('T')[0],
        imageUrl: data.imageUrl
      };
      news.unshift(newItem);
      await saveData(NEWS_FILE, news);
      sendUpdateToAll(); // Send update notification
      return NextResponse.json(newItem);
    } else {
      // Update existing news item
      const index = news.findIndex(item => item.id === data.id);
      if (index !== -1) {
        news[index] = { ...news[index], ...data } as NewsItem;
        await saveData(NEWS_FILE, news);
        sendUpdateToAll(); // Send update notification
        return NextResponse.json(news[index]);
      }
      return NextResponse.json(
        { error: 'الخبر غير موجود' },
        { status: 404 }
      );
    }
  } catch (error) {
    console.error('Error saving news:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء حفظ الأخبار' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    const news = await loadData<NewsItem[]>(NEWS_FILE, []);
    const newNews = news.filter(item => item.id !== id);
    await saveData(NEWS_FILE, newNews);
    sendUpdateToAll(); // Send update notification
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting news:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء حذف الخبر' },
      { status: 500 }
    );
  }
} 