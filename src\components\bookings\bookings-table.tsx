"use client";

import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, ArrowUpDown } from "lucide-react";
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

interface Booking {
  name: string;
  phone: string;
  nationality: string;
  birthDate: string;
  email: string;
  packageTitle: string;
  packageType: 'Hajj' | 'Umrah';
  submittedAt: string;
  status?: 'pending' | 'contacted' | 'confirmed' | 'cancelled';
}

interface BookingsTableProps {
  bookings: Booking[];
  onUpdateStatus: (booking: Booking, status: Booking['status']) => void;
}

export function BookingsTable({ bookings, onUpdateStatus }: BookingsTableProps) {
  const [sortField, setSortField] = useState<keyof Booking>('submittedAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  const sortedBookings = [...bookings].sort((a, b) => {
    if (sortField === 'submittedAt') {
      return sortDirection === 'asc' 
        ? new Date(a[sortField]).getTime() - new Date(b[sortField]).getTime()
        : new Date(b[sortField]).getTime() - new Date(a[sortField]).getTime();
    }
    return sortDirection === 'asc'
      ? a[sortField].localeCompare(b[sortField])
      : b[sortField].localeCompare(a[sortField]);
  });

  const handleSort = (field: keyof Booking) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getStatusColor = (status: Booking['status']) => {
    switch (status) {
      case 'pending': return 'bg-yellow-500';
      case 'contacted': return 'bg-blue-500';
      case 'confirmed': return 'bg-green-500';
      case 'cancelled': return 'bg-red-500';
      default: return 'bg-yellow-500';
    }
  };

  const getStatusText = (status: Booking['status']) => {
    switch (status) {
      case 'pending': return 'قيد الانتظار';
      case 'contacted': return 'تم التواصل';
      case 'confirmed': return 'تم التأكيد';
      case 'cancelled': return 'ملغي';
      default: return 'قيد الانتظار';
    }
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[200px]">
              <Button variant="ghost" onClick={() => handleSort('name')}>
                الاسم
                <ArrowUpDown className="mr-2 h-4 w-4" />
              </Button>
            </TableHead>
            <TableHead>رقم الجوال</TableHead>
            <TableHead>
              <Button variant="ghost" onClick={() => handleSort('packageType')}>
                نوع الباقة
                <ArrowUpDown className="mr-2 h-4 w-4" />
              </Button>
            </TableHead>
            <TableHead>اسم الباقة</TableHead>
            <TableHead>
              <Button variant="ghost" onClick={() => handleSort('submittedAt')}>
                تاريخ الطلب
                <ArrowUpDown className="mr-2 h-4 w-4" />
              </Button>
            </TableHead>
            <TableHead>الحالة</TableHead>
            <TableHead className="text-left">الإجراءات</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedBookings.map((booking) => (
            <TableRow key={booking.submittedAt}>
              <TableCell className="font-medium">{booking.name}</TableCell>
              <TableCell dir="ltr">{booking.phone}</TableCell>
              <TableCell>
                <Badge variant={booking.packageType === 'Hajj' ? 'default' : 'secondary'}>
                  {booking.packageType === 'Hajj' ? 'حج' : 'عمرة'}
                </Badge>
              </TableCell>
              <TableCell>{booking.packageTitle}</TableCell>
              <TableCell>
                {format(new Date(booking.submittedAt), 'PPP', { locale: ar })}
              </TableCell>
              <TableCell>
                <Badge className={getStatusColor(booking.status)}>
                  {getStatusText(booking.status)}
                </Badge>
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>تغيير الحالة</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => onUpdateStatus(booking, 'pending')}>
                      قيد الانتظار
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onUpdateStatus(booking, 'contacted')}>
                      تم التواصل
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onUpdateStatus(booking, 'confirmed')}>
                      تم التأكيد
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onUpdateStatus(booking, 'cancelled')}>
                      إلغاء
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
} 