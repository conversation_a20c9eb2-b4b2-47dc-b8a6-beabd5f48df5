import { PrismaClient } from '@prisma/client';
import { hash } from 'bcryptjs';

async function recreateAdmin() {
  const prisma = new PrismaClient();
  const email = '<EMAIL>';
  const password = 'Ithaar@2024';

  try {
    // Delete existing admin if exists
    await prisma.user.deleteMany({
      where: { email }
    });

    console.log('Deleted existing admin user');

    // Hash the password
    const hashedPassword = await hash(password, 10);
    console.log('Password hashed');

    // Create new admin user
    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        role: 'ADMIN'
      }
    });

    console.log('Created new admin user:', {
      id: user.id,
      email: user.email,
      role: user.role,
      passwordLength: user.password.length,
      passwordStart: user.password.substring(0, 10) + '...'
    });

    console.log('\nYou can now login with:');
    console.log('Email:', email);
    console.log('Password:', password);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

recreateAdmin();