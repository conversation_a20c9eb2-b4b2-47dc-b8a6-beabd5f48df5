export interface HeroContent {
  backgroundImageUrl: string;
  backgroundOpacity: number;
  titleColor: string;
  subtitleColor: string;
  dataAiHint: string;
  title: string;
  subtitle: string;
}

export interface TripItem {
  id: number;
  imageUrl: string;
  dataAiHint: string;
  title: string;
  description: string;
}

export enum MediaType {
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO'
}

export enum MediaCategory {
  PREVIOUS_TRIP = 'PREVIOUS_TRIP',
  GALLERY = 'GALLERY',
  NEWS = 'NEWS',
  PAGE_BACKGROUND = 'PAGE_BACKGROUND',
  OTHER = 'OTHER'
}

export enum ContentType {
  HOMEPAGE = 'HOMEPAGE',
  ABOUT = 'ABOUT',
  SERVICES = 'SERVICES',
  CONTACT = 'CONTACT',
  PAGE = 'PAGE'
}

export interface MediaImage {
  id: string;
  url: string;
  title: string;
  description?: string;
  category?: MediaCategory;
  type: MediaType;
  thumbnailUrl?: string | null;
  groupId?: string;
  groupTitle?: string;
} 