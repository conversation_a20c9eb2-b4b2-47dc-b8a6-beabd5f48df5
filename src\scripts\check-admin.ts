import { PrismaClient } from '@prisma/client';

async function checkAdmin() {
  const prisma = new PrismaClient();

  try {
    const adminEmail = process.env.INITIAL_ADMIN_EMAIL || '<EMAIL>';
    
    console.log('Checking admin user:', adminEmail);

    const user = await prisma.user.findUnique({
      where: { email: adminEmail }
    });

    if (user) {
      console.log('Admin user found:', {
        id: user.id,
        email: user.email,
        role: user.role,
        createdAt: user.created_at,
        hasPassword: !!user.password
      });
    } else {
      console.log('Admin user not found');
      
      // Try to recreate admin user
      console.log('Attempting to create admin user...');
      const password = process.env.INITIAL_ADMIN_PASSWORD || 'Ithaar@2024';
      
      // Import hash here to avoid issues with bcryptjs in global scope
      const { hash } = require('bcryptjs');
      const hashedPassword = await hash(password, 10);

      const newAdmin = await prisma.user.create({
        data: {
          email: adminEmail,
          password: hashedPassword,
          role: 'ADMIN'
        }
      });

      console.log('Created new admin user:', {
        id: newAdmin.id,
        email: newAdmin.email,
        role: newAdmin.role,
        createdAt: newAdmin.created_at
      });
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAdmin();