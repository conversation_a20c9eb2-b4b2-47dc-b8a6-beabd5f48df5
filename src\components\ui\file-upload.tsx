'use client';

import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { UploadCloud, X } from 'lucide-react';
import Image from 'next/image';
import { Button } from './button';

interface FileUploadProps {
  onFileSelect: (files: File[]) => void;
  onClear?: () => void;
  accept?: string[];
  maxSize?: number;
  previews?: string[];
  multiple?: boolean;
  className?: string;
}

export function FileUpload({
  onFileSelect,
  onClear,
  accept = ['image/jpeg', 'image/png', 'image/webp'],
  maxSize = 5 * 1024 * 1024, // 5MB
  previews = [],
  multiple = false,
  className = '',
}: FileUploadProps) {
  const [previewUrls, setPreviewUrls] = useState<string[]>(previews);
  const [error, setError] = useState<string | null>(null);

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    if (rejectedFiles.length > 0) {
      const errors = rejectedFiles[0].errors.map((err: any) => {
        if (err.code === 'file-too-large') {
          return 'الملف كبير جداً. الحد الأقصى 5 ميجابايت';
        }
        if (err.code === 'file-invalid-type') {
          return 'نوع الملف غير مدعوم. يرجى اختيار صورة';
        }
        return err.message;
      });
      setError(errors.join(', '));
      return;
    }

    if (acceptedFiles.length > 0) {
      onFileSelect(acceptedFiles);
      const newPreviews = acceptedFiles.map(file => URL.createObjectURL(file));
      setPreviewUrls(prev => multiple ? [...prev, ...newPreviews] : [newPreviews[0]]);
      setError(null);
    }
  }, [onFileSelect, multiple]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: accept.reduce((acc, curr) => ({ ...acc, [curr]: [] }), {}),
    maxSize,
    multiple
  });

  const handleClear = (index?: number) => {
    if (typeof index === 'number') {
      setPreviewUrls(prev => prev.filter((_, i) => i !== index));
    } else {
      setPreviewUrls([]);
      if (onClear) onClear();
    }
    setError(null);
  };

  return (
    <div className={`w-full ${className}`}>
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors mb-4
          ${isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25 hover:border-primary/50'}
          ${error ? 'border-destructive' : ''}`}
      >
        <input {...getInputProps()} />
        <UploadCloud className="mx-auto h-12 w-12 text-muted-foreground" />
        <p className="mt-2 text-sm text-muted-foreground">
          {isDragActive ? (
            'أفلت الملفات هنا...'
          ) : (
            <>
              اسحب وأفلت الملفات هنا، أو <span className="text-primary">اختر ملفات</span>
            </>
          )}
        </p>
        <p className="mt-1 text-xs text-muted-foreground">
          PNG, JPG أو WEBP حتى 5 ميجابايت {multiple ? '(يمكنك اختيار عدة صور)' : ''}
        </p>
        {error && <p className="mt-2 text-sm text-destructive">{error}</p>}
      </div>

      {previewUrls.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {previewUrls.map((url, index) => (
            <div key={url} className="relative rounded-lg overflow-hidden">
              <Image
                src={url}
                alt={`Preview ${index + 1}`}
                width={200}
                height={150}
                className="w-full h-32 object-cover"
              />
              <Button
                type="button"
                variant="destructive"
                size="icon"
                className="absolute top-2 right-2"
                onClick={() => handleClear(index)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
} 