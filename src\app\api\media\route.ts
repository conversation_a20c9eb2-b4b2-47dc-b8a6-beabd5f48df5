import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { MediaType, MediaCategory } from '@prisma/client';
import { sendUpdateToAll } from '../updates/route';

export const dynamic = 'force-dynamic'; // تعطيل التخزين المؤقت على مستوى الصفحة
export const revalidate = 0; // تعطيل التخزين المؤقت

// دالة مشتركة لتجميع الوسائط
async function getGroupedMedia(type: MediaType | null, category: MediaCategory | null) {
  const media = await prisma.media.findMany({
    where: {
      ...(type && { type }),
      ...(category && { category })
    },
    orderBy: {
      created_at: 'desc'
    }
  });

  return media.map(item => {
    const baseTitle = item.title.replace(/ - \d+$/, '');
    return {
      ...item,
      groupId: baseTitle,
      groupTitle: baseTitle
    };
  });
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') as MediaType | null;
    const category = searchParams.get('category') as MediaCategory | null;
    
    const groupedMedia = await getGroupedMedia(type, category);

    return new NextResponse(JSON.stringify(groupedMedia), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    console.error('Error fetching media:', error);
    return NextResponse.json(
      { error: 'Error fetching media' },
      { 
        status: 500,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      }
    );
  }
}

export async function POST(request: Request) {
  try {
    const data = await request.json();
    const result = await prisma.media.create({ data });
    sendUpdateToAll(); // إرسال تحديث للمتصلين
    return NextResponse.json(result);
  } catch (error) {
    return NextResponse.json({ error: 'Error creating media' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const data = await request.json();
    const result = await prisma.media.update({
      where: { id: data.id },
      data
    });
    sendUpdateToAll(); // إرسال تحديث للمتصلين
    return NextResponse.json(result);
  } catch (error) {
    return NextResponse.json({ error: 'Error updating media' }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    if (!id) throw new Error('ID is required');
    
    const result = await prisma.media.delete({
      where: { id }
    });
    sendUpdateToAll(); // إرسال تحديث للمتصلين
    return NextResponse.json(result);
  } catch (error) {
    return NextResponse.json({ error: 'Error deleting media' }, { status: 500 });
  }
} 