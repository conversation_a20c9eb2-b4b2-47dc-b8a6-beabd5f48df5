'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Star } from 'lucide-react';

interface Comment {
  id: string;
  author: string;
  content: string;
  rating: number;
  date: string;
}

function RatingStars({ rating = 5 }: { rating?: number }) {
  return (
    <div className="flex items-center gap-0.5">
      {[...Array(5)].map((_, i) => (
        <Star
          key={i}
          className={`h-5 w-5 ${
            i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
          }`}
        />
      ))}
    </div>
  );
}

export function ReviewsList() {
  const [comments, setComments] = useState<Comment[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchComments();
  }, []);

  const fetchComments = async () => {
    try {
      const response = await fetch('/api/comments');
      if (!response.ok) {
        throw new Error('Failed to fetch comments');
      }
      const data = await response.json();
      setComments(data);
    } catch (error) {
      console.error('Error fetching comments:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="grid md:grid-cols-2 gap-8">
        {[...Array(4)].map((_, index) => (
          <Card key={index} className="shadow-lg">
            <CardHeader>
              <div className="flex items-center mb-4">
                <div className="h-12 w-12 rounded-full bg-muted animate-pulse ml-4 rtl:mr-4" />
                <div className="space-y-2">
                  <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                  <div className="h-3 w-24 bg-muted animate-pulse rounded" />
                </div>
              </div>
              <div className="flex gap-0.5">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="h-5 w-5 bg-muted animate-pulse rounded" />
                ))}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="h-4 w-full bg-muted animate-pulse rounded" />
                <div className="h-4 w-3/4 bg-muted animate-pulse rounded" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-12">
      <div className="grid md:grid-cols-2 gap-8">
        {comments.map((comment) => (
          <Card key={comment.id} className="shadow-lg hover:shadow-xl transition-shadow duration-300">
            <CardHeader>
              <div className="flex items-center mb-4">
                <Avatar className="h-12 w-12 ml-4 rtl:mr-4">
                  <AvatarFallback>{comment.author[0].toUpperCase()}</AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle className="text-lg">{comment.author}</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    {new Date(comment.date).toLocaleDateString('ar-SA')}
                  </p>
                </div>
              </div>
              <RatingStars rating={comment.rating} />
            </CardHeader>
            <CardContent>
              <p className="text-foreground leading-relaxed">{comment.content}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      <section className="text-center py-10">
        <h2 className="text-2xl font-semibold text-foreground mb-4">شاركنا تجربتك!</h2>
        <p className="text-muted-foreground mb-6">
          إذا كنت قد شرفتنا بخدمتك، نود أن نسمع رأيك. ملاحظاتك تساعدنا على تطوير خدماتنا.
        </p>
        <a href="/contact" className="text-primary hover:underline">
          اترك تقييمك هنا
        </a>
      </section>
    </div>
  );
} 