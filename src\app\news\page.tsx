'use client';

import { useState, useEffect } from 'react';
import PageHeader from '@/components/layout/PageHeader';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import { Calendar, User } from 'lucide-react';

interface PageContent {
  backgroundImage?: string;
}

interface NewsItem {
  id: string;
  title: string;
  content: string;
  publish_date: string;
  author: {
    email: string;
  };
}

export default function NewsPage() {
  const [pageContent, setPageContent] = useState<PageContent>({});
  const [news, setNews] = useState<NewsItem[]>([]);
  const [selectedNews, setSelectedNews] = useState<NewsItem | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch page content
        const pageResponse = await fetch('/api/pages');
        if (pageResponse.ok) {
          const pageData = await pageResponse.json();
          setPageContent(pageData.news || {});
        }

        // Fetch news items
        const newsResponse = await fetch('/api/news');
        if (newsResponse.ok) {
          const newsData = await newsResponse.json();
          setNews(newsData);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="space-y-16 md:space-y-20 fade-in-up">
      <PageHeader
        title="آخر الأخبار"
        subtitle="تابع آخر أخبار وتحديثات رحلات الحج والعمرة وأنشطة شركة إيثار."
        backgroundImage={pageContent.backgroundImage}
      />
      
      <section className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {news.map((item) => (
            <Card 
              key={item.id} 
              className="overflow-hidden cursor-pointer hover:shadow-lg transition-all duration-300"
              onClick={() => setSelectedNews(item)}
            >
              <div className="relative aspect-video">
                <Image
                  src={`https://placehold.co/600x400.png?text=${encodeURIComponent(item.title)}`}
                  alt={item.title}
                  fill
                  className="object-cover"
                />
              </div>
              <CardContent className="p-6">
                <CardHeader className="px-0 pt-0">
                  <CardTitle className="text-xl font-bold line-clamp-2">
                    {item.title}
                  </CardTitle>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground mt-2">
                    <Calendar className="h-4 w-4" />
                    <time dateTime={item.publish_date}>
                      {new Date(item.publish_date).toLocaleDateString('ar-SA')}
                    </time>
                  </div>
                </CardHeader>
                <p className="text-muted-foreground line-clamp-3 mt-4">
                  {item.content}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* News Details Modal */}
      <Dialog open={!!selectedNews} onOpenChange={(open) => !open && setSelectedNews(null)}>
        <DialogContent className="max-w-3xl">
          {selectedNews && (
            <>
              <DialogHeader>
                <DialogTitle className="text-2xl">{selectedNews.title}</DialogTitle>
                <DialogDescription>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground mt-2">
                    <div className="flex items-center gap-1">
                      <User className="h-4 w-4" />
                      <span>{selectedNews.author.email}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>{new Date(selectedNews.publish_date).toLocaleDateString('ar-SA')}</span>
                    </div>
                  </div>
                </DialogDescription>
              </DialogHeader>
              
              <div className="relative h-64 w-full mb-4 rounded-lg overflow-hidden">
                <Image
                  src={`https://placehold.co/600x400.png?text=${encodeURIComponent(selectedNews.title)}`}
                  alt={selectedNews.title}
                  fill
                  className="object-cover"
                />
              </div>
              
              <div className="prose prose-lg max-w-none">
                <p>{selectedNews.content}</p>
              </div>
              
              <div className="mt-6 flex justify-end">
                <Button variant="outline" onClick={() => setSelectedNews(null)}>
                  إغلاق
                </Button>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
} 