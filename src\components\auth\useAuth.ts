'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import type { User } from '@prisma/client';

interface AuthState {
  user: User | null;
  loading: boolean;
  isAdmin: boolean;
}

export function useAuth() {
  const [state, setState] = useState<AuthState>({
    user: null,
    loading: true,
    isAdmin: false
  });
  
  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    async function checkAuth() {
      try {
        const res = await fetch('/api/auth/check', {
          credentials: 'include'
        });
        const data = await res.json();

        if (data.success) {
          setState({
            user: data.user,
            loading: false,
            isAdmin: data.user.role === 'ADMIN'
          });
        } else {
          setState({
            user: null,
            loading: false,
            isAdmin: false
          });
          // Redirect if on admin page
          if (pathname.startsWith('/admin')) {
            router.push(`/login?redirect=${pathname}`);
          }
        }
      } catch (error) {
        setState({
          user: null,
          loading: false,
          isAdmin: false
        });
      }
    }

    checkAuth();
  }, [pathname, router]);

  const login = async (email: string, password: string) => {
    try {
      const res = await fetch('/api/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await res.json();
      if (data.success) {
        setState({
          user: data.user,
          loading: false,
          isAdmin: data.user.role === 'ADMIN'
        });
        return { success: true };
      } else {
        return { success: false, error: data.error };
      }
    } catch (error) {
      return {
        success: false,
        error: 'حدث خطأ في تسجيل الدخول'
      };
    }
  };

  const logout = async () => {
    try {
      await fetch('/api/auth', {
        method: 'DELETE',
        credentials: 'include'
      });
      setState({
        user: null,
        loading: false,
        isAdmin: false
      });
      router.push('/');
      router.refresh();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return {
    ...state,
    login,
    logout
  };
}