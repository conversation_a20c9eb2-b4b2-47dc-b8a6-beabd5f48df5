'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { PlayCircle, Image as ImageIconLucide, Loader2 } from 'lucide-react';
import { MediaType, MediaCategory } from '@prisma/client';
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { PhotoProvider, PhotoView } from 'react-photo-view';
import 'react-photo-view/dist/react-photo-view.css';
import PageHeader from '@/components/layout/PageHeader';
import { MediaGallery } from '@/components/media/MediaGallery';

interface MediaItem {
  id: string;
  type: MediaType;
  url: string;
  title: string;
  description?: string;
  category: MediaCategory;
  created_at: string;
  thumbnailUrl?: string | null;
  groupId?: string;
  groupTitle?: string;
}

interface MediaGroup {
  id: string;
  title: string;
  description?: string;
  category: MediaCategory;
  items: MediaItem[];
  type: MediaType;
  thumbnailUrl?: string;
}

interface PageContent {
  backgroundImage?: string;
}

export default function MediaPage() {
  const [pageContent, setPageContent] = useState<PageContent>({});

  useEffect(() => {
    const fetchPageContent = async () => {
      try {
        const response = await fetch('/api/pages');
        if (response.ok) {
          const data = await response.json();
          setPageContent(data.media || {});
        }
      } catch (error) {
        console.error('Error fetching page content:', error);
      }
    };

    fetchPageContent();
  }, []);

  return (
    <div className="space-y-16 md:space-y-20 fade-in-up">
      <PageHeader
        title="مكتبة الوسائط"
        subtitle="مجموعة من الصور والفيديوهات التي توثق رحلات الحج والعمرة السابقة وتعكس جودة خدماتنا."
        backgroundImage={pageContent.backgroundImage}
      />
      
      <section className="container mx-auto px-4">
        <MediaGallery />
      </section>
    </div>
  );
}

function getVideoThumbnail(item: MediaItem): string {
  if (item.thumbnailUrl) return item.thumbnailUrl;
  
  // Handle YouTube videos
  if (item.url.includes('youtube.com') || item.url.includes('youtu.be')) {
    const videoId = item.url.includes('youtube.com') 
      ? item.url.split('v=')[1]?.split('&')[0]
      : item.url.split('/').pop();
    return videoId ? `https://img.youtube.com/vi/${videoId}/mqdefault.jpg` : 'https://placehold.co/600x400?text=Video';
  }
  
  return 'https://placehold.co/600x400?text=Video';
}

function getCategoryLabel(category: MediaCategory): string {
  const labels = {
    [MediaCategory.PREVIOUS_TRIP]: 'رحلات سابقة',
    [MediaCategory.GALLERY]: 'معرض الصور',
    [MediaCategory.NEWS]: 'الأخبار',
    [MediaCategory.OTHER]: 'أخرى'
  };
  return labels[category] || category;
}
