import { prisma } from '@/lib/prisma';
import { HeroContent, TripItem } from '@/lib/types';
import type { MediaType, MediaCategory } from '@/types';

export async function getHomepageContent() {
  const content = await prisma.content.findFirst({
    where: {
      type: 'HOMEPAGE'
    }
  });

  const contentData = content?.content ? JSON.parse(content.content) : {};
  
  const hero: HeroContent = {
    backgroundImageUrl: contentData.backgroundImageUrl || '',
    backgroundOpacity: contentData.backgroundOpacity || 0.5,
    titleColor: contentData.titleColor || '#FFFFFF',
    subtitleColor: contentData.subtitleColor || '#FFFFFF',
    dataAiHint: 'kaaba mecca',
    title: content?.title || '',
    subtitle: contentData.subtitle || '',
  };

  const latestTrips = await prisma.media.findMany({
    where: {
      type: 'IMAGE' as MediaType,
      category: 'PREVIOUS_TRIP' as MediaCategory
    },
    take: 4,
    orderBy: {
      created_at: 'desc'
    }
  });

  const formattedTrips: TripItem[] = latestTrips.map((trip: any, index: number) => ({
    id: index + 1,
    imageUrl: trip.url,
    dataAiHint: trip.description || 'trip image',
    title: trip.title,
    description: trip.description || '',
  }));

  return {
    hero,
    latestTrips: formattedTrips
  };
}

export async function updateHomepageContent(data: {
  title: string;
  subtitle: string;
  backgroundImageUrl: string;
  backgroundOpacity: number;
  titleColor: string;
  subtitleColor: string;
  latestTrips: {
    id: number;
    imageUrl: string;
    dataAiHint: string;
    title: string;
    description: string;
  }[];
}) {
  // First try to find existing content
  const existingContent = await prisma.content.findFirst({
    where: {
      type: 'HOMEPAGE'
    }
  });

  // Parse existing content or create empty object if none exists
  const existingContentData = existingContent?.content 
    ? JSON.parse(existingContent.content)
    : {};

  // Update homepage content
  const updatedContent = existingContent
    ? await prisma.content.update({
        where: {
          id: existingContent.id
        },
        data: {
          title: data.title || existingContent.title,
          content: JSON.stringify({
            ...existingContentData,
            subtitle: data.subtitle,
            backgroundImageUrl: data.backgroundImageUrl,
            backgroundOpacity: data.backgroundOpacity,
            titleColor: data.titleColor,
            subtitleColor: data.subtitleColor
          })
        }
      })
    : await prisma.content.create({
        data: {
          type: 'HOMEPAGE',
          title: data.title,
          content: JSON.stringify({
            subtitle: data.subtitle,
            backgroundImageUrl: data.backgroundImageUrl,
            backgroundOpacity: data.backgroundOpacity,
            titleColor: data.titleColor,
            subtitleColor: data.subtitleColor
          })
        }
      });

  // Update latest trips in media table
  // First, delete existing media entries of type IMAGE with category PREVIOUS_TRIP
  await prisma.media.deleteMany({
    where: {
      type: 'IMAGE' as MediaType,
      category: 'PREVIOUS_TRIP' as MediaCategory
    }
  });

  // Then create new entries for each trip
  await prisma.media.createMany({
    data: data.latestTrips.map(trip => ({
      type: 'IMAGE' as MediaType,
      url: trip.imageUrl,
      title: trip.title,
      description: trip.description,
      category: 'PREVIOUS_TRIP' as MediaCategory,
      created_at: new Date()
    }))
  });

  return updatedContent;
} 