import { Home, Users, Briefcase, ImageIcon, Star, HelpCircle, Phone, ShieldCheck, Newspaper } from 'lucide-react';

export interface NavItem {
  label: string;
  href: string;
  icon: any;
  admin?: boolean;
}

export const NAV_LINKS: NavItem[] = [
  { label: 'الرئيسية', href: '/', icon: Home },
  { label: 'من نحن', href: '/about', icon: Users },
  { label: 'خدماتنا', href: '/services', icon: Briefcase },
  { label: 'آخر الأخبار', href: '/news', icon: Newspaper },
  { label: 'مكتبة الوسائط', href: '/media', icon: ImageIcon },
  { label: 'آراء العملاء', href: '/reviews', icon: Star },
  { label: 'الأسئلة الشائعة', href: '/faq', icon: HelpCircle },
  { label: 'تواصل معنا', href: '/contact', icon: Phone },
  { label: 'لوحة التحكم', href: '/admin', admin: true, icon: ShieldCheck },
];

export const WHATSAPP_NUMBER = "9665XXXXXXXX"; // Replace with actual WhatsApp number
export const WHATSAPP_MESSAGE = "السلام عليكم، أود الاستفسار عن خدماتكم.";

