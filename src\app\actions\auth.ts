'use server';

import { hash, compare } from 'bcryptjs';
import { sign } from 'jsonwebtoken';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { ResponseCookie } from 'next/dist/compiled/@edge-runtime/cookies';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const AUTH_COOKIE = 'auth';

function setAuthCookie(token: string): ResponseCookie {
  return {
    name: AUTH_COOKIE,
    value: token,
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 60 * 60 * 24 * 7 // 7 days
  };
}

export async function login(email: string, password: string) {
  try {
    // Find user by email
    const user = await prisma.user.findUnique({ 
      where: { email } 
    });

    if (!user) {
      return {
        success: false,
        error: 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
      };
    }

    // Verify password
    const validPassword = await compare(password, user.password);
    if (!validPassword) {
      return {
        success: false,
        error: 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
      };
    }

    // Check if user is admin
    if (user.role !== 'ADMIN') {
      return {
        success: false,
        error: 'غير مصرح لك بالدخول'
      };
    }

    // Generate JWT token
    const token = sign(
      { userId: user.id, email: user.email, role: user.role },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    // Set auth cookie
    const cookie = setAuthCookie(token);
    
    return new Response(JSON.stringify({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role
      }
    }), {
      headers: {
        'Set-Cookie': `${cookie.name}=${cookie.value}; Max-Age=${cookie.maxAge}; Path=/; HttpOnly; ${cookie.secure ? 'Secure; ' : ''}SameSite=${cookie.sameSite}`,
      }
    });

  } catch (error: any) {
    console.error('Auth Error:', error);
    return {
      success: false,
      error: 'حدث خطأ في تسجيل الدخول'
    };
  }
}

export async function logout() {
  return new Response(null, {
    headers: {
      'Set-Cookie': `${AUTH_COOKIE}=; Max-Age=0; Path=/; HttpOnly`
    }
  });
}

export async function createAdmin(email: string, password: string) {
  try {
    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return {
        success: false,
        error: 'البريد الإلكتروني مستخدم بالفعل'
      };
    }

    // Hash password
    const hashedPassword = await hash(password, 10);

    // Create admin user
    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        role: 'ADMIN'
      }
    });

    return {
      success: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role
      }
    };
  } catch (error: any) {
    console.error('Create Admin Error:', error);
    return {
      success: false,
      error: 'حدث خطأ في إنشاء المستخدم'
    };
  }
}