import { NextResponse } from 'next/server';
import { loadData, saveData } from '@/lib/data-utils';
import { sendUpdateToAll } from '../updates/route';

interface NewsTickerItem {
  id: string;
  text: string;
  link?: string;
  icon?: string;
}

interface NewsTickerData {
  items: NewsTickerItem[];
  speed: number;
}

const NEWS_TICKER_FILE = 'news-ticker.json';

export async function GET() {
  try {
    const data = await loadData<NewsTickerData>(NEWS_TICKER_FILE, {
      items: [
        {
          id: '1',
          text: 'مرحباً بكم في موقع إيثار لخدمات الحج والعمرة',
        },
        {
          id: '2',
          text: 'نقدم أفضل الخدمات لضيوف الرحمن',
        },
        {
          id: '3',
          text: 'تصفح باقاتنا المميزة للحج والعمرة',
          link: '/services',
        },
      ],
      speed: 30,
    });
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching news ticker:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب شريط الأخبار' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const data = await request.json() as NewsTickerData;
    await saveData(NEWS_TICKER_FILE, data);
    sendUpdateToAll(); // Send update notification
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error saving news ticker:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء حفظ شريط الأخبار' },
      { status: 500 }
    );
  }
} 