'use client';

import { useState, useEffect } from 'react';
import NextImage from "@/components/ui/next-image";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { FileUpload } from "@/components/ui/file-upload";
import { ImageSlider } from "@/components/ui/image-slider";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Pencil, Trash2, Image as ImageIcon } from "lucide-react";

// Define enums locally to avoid Prisma Client issues
export enum MediaType {
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO'
}

export enum MediaCategory {
  PREVIOUS_TRIP = 'PREVIOUS_TRIP',
  GALLERY = 'GALLERY',
  NEWS = 'NEWS',
  PAGE_BACKGROUND = 'PAGE_BACKGROUND',
  OTHER = 'OTHER'
}

interface MediaImage {
  id: string;
  url: string;
  title: string;
  description: string;
  category?: MediaCategory;
  type: MediaType;
  thumbnailUrl?: string | null;
  groupId?: string;
  groupTitle?: string;
}

interface ImageGroup {
  id: string;
  title: string;
  description: string;
  category?: MediaCategory;
  images: MediaImage[];
}

function getVideoThumbnail(item: MediaImage): string {
  if (item.thumbnailUrl) return item.thumbnailUrl;
  
  // Handle YouTube videos
  if (item.type === 'VIDEO' && (item.url.includes('youtube.com') || item.url.includes('youtu.be'))) {
    const videoId = item.url.includes('youtube.com') 
      ? item.url.split('v=')[1]?.split('&')[0]
      : item.url.split('/').pop();
    return videoId ? `https://img.youtube.com/vi/${videoId}/mqdefault.jpg` : 'https://placehold.co/600x400?text=Video';
  }
  
  return item.url;
}

export default function AdminMediaImagesPage() {
  const [images, setImages] = useState<MediaImage[]>([]);
  const [imageGroups, setImageGroups] = useState<ImageGroup[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState<MediaImage | null>(null);
  const [selectedGroup, setSelectedGroup] = useState<ImageGroup | null>(null);
  const [isSliderOpen, setIsSliderOpen] = useState(false);
  const [sliderImages, setSliderImages] = useState<MediaImage[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const { toast } = useToast();

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: ''
  });

  useEffect(() => {
    fetchImages();
  }, []);

  const fetchImages = async () => {
    try {
      const response = await fetch('/api/media?type=IMAGE');
      if (!response.ok) throw new Error('Failed to fetch images');
      const data = await response.json() as MediaImage[];
      setImages(data);
      
      // Group images by groupId
      const groups = data.reduce((acc: ImageGroup[], img) => {
        if (img.groupId) {
          const existingGroup = acc.find(g => g.id === img.groupId);
          if (existingGroup) {
            existingGroup.images.push(img);
          } else if (img.groupTitle) {
            acc.push({
              id: img.groupId,
              title: img.groupTitle,
              description: img.description,
              category: img.category,
              images: [img]
            });
          }
        }
        return acc;
      }, []);
      
      setImageGroups(groups);
    } catch (error) {
      toast({
        title: "خطأ في تحميل الصور",
        description: "حدث خطأ أثناء تحميل الصور. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAdd = () => {
    setFormData({ title: '', description: '', category: '' });
    setUploadedFiles([]);
    setIsAddDialogOpen(true);
  };

  const handleEdit = (image: MediaImage) => {
    setSelectedImage(image);
    setFormData({
      title: image.title,
      description: image.description || '',
      category: image.category || ''
    });
    setIsEditDialogOpen(true);
  };

  const handleDelete = (image: MediaImage) => {
    setSelectedImage(image);
    setIsDeleteDialogOpen(true);
  };

  const handleGroupClick = (group: ImageGroup) => {
    if (group.images && group.images.length > 0) {
      setSliderImages(group.images);
      setIsSliderOpen(true);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      if (selectedImage) {
        // Edit existing image
        const response = await fetch(`/api/media/${selectedImage.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        });

        if (!response.ok) throw new Error('Failed to update image');

        const updatedImage = await response.json();
        setImages(images.map(img => 
          img.id === selectedImage.id ? updatedImage : img
        ));

        toast({
          title: "تم التعديل بنجاح",
          description: "تم تحديث بيانات الصورة",
        });
        setIsEditDialogOpen(false);
      } else {
        // Add new images
        if (uploadedFiles.length === 0) {
          toast({
            title: "خطأ",
            description: "يرجى اختيار صورة واحدة على الأقل للرفع",
            variant: "destructive",
          });
          return;
        }

        const groupId = Date.now().toString();
        const uploadPromises = uploadedFiles.map(async (file, index) => {
          const formDataToSend = new FormData();
          formDataToSend.append('file', file);
          formDataToSend.append('title', `${formData.title} - ${index + 1}`);
          formDataToSend.append('description', formData.description);
          if (formData.category) {
            formDataToSend.append('category', formData.category);
          }

          const response = await fetch('/api/media/upload', {
            method: 'POST',
            body: formDataToSend,
          });

          if (!response.ok) throw new Error('Failed to upload image');
          return response.json();
        });

        const newImages = await Promise.all(uploadPromises);
        
        // Create a new group
        const newGroup: ImageGroup = {
          id: groupId,
          title: formData.title,
          description: formData.description,
          category: formData.category as MediaCategory,
          images: newImages.map(img => ({
            ...img,
            groupId,
            groupTitle: formData.title
          }))
        };

        setImageGroups(prev => [newGroup, ...prev]);
        setImages(prev => [...newImages, ...prev]);

        toast({
          title: "تمت الإضافة بنجاح",
          description: `تم إضافة ${newImages.length} صور جديدة`,
        });
        setIsAddDialogOpen(false);
      }
    } catch (error) {
      toast({
        title: "حدث خطأ",
        description: "فشلت العملية، يرجى المحاولة مرة أخرى",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleConfirmDelete = async () => {
    if (selectedImage) {
      try {
        const response = await fetch(`/api/media/${selectedImage.id}`, {
          method: 'DELETE',
        });

        if (!response.ok) throw new Error('Failed to delete image');

        setImages(images.filter(img => img.id !== selectedImage.id));
        
        // Update groups
        if (selectedImage.groupId) {
          setImageGroups(prev => {
            const updatedGroups = prev.map(group => {
              if (group.id === selectedImage.groupId) {
                const updatedImages = group.images.filter(img => img.id !== selectedImage.id);
                return updatedImages.length > 0 
                  ? { ...group, images: updatedImages }
                  : null;
              }
              return group;
            }).filter((group): group is ImageGroup => group !== null);
            return updatedGroups;
          });
        }

        toast({
          title: "تم الحذف",
          description: "تم حذف الصورة بنجاح",
        });
      } catch (error) {
        toast({
          title: "حدث خطأ",
          description: "فشل حذف الصورة، يرجى المحاولة مرة أخرى",
          variant: "destructive",
        });
      }
    }
    setIsDeleteDialogOpen(false);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold">إدارة الصور</h2>
        <Button onClick={handleAdd} className="bg-primary text-white">
          إضافة صور جديدة
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {imageGroups.map((group) => (
          <Card key={group.id} className="overflow-hidden group cursor-pointer" onClick={() => handleGroupClick(group)}>
            <div className="relative aspect-video">
              <NextImage
                src={group.images[0].url}
                alt={group.title}
                fill
                className="object-cover"
                sizes="(min-width: 1280px) 278px, (min-width: 1040px) calc(33.3vw - 32px), (min-width: 780px) calc(50vw - 40px), calc(100vw - 32px)"
              />
              {group.images.length > 1 && (
                <div className="absolute bottom-2 right-2 bg-background/80 px-2 py-1 rounded text-sm">
                  <ImageIcon className="h-4 w-4 inline-block ml-1" />
                  {group.images.length}
                </div>
              )}
            </div>
            <div className="p-4">
              <h3 className="font-semibold text-lg">{group.title}</h3>
              <p className="text-muted-foreground">{group.description}</p>
              {group.category && (
                <p className="text-sm text-muted-foreground mt-1">الفئة: {group.category}</p>
              )}
              <div className="flex justify-end gap-2 mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-blue-600 hover:text-blue-800"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEdit(group.images[0]);
                  }}
                >
                  <Pencil className="h-4 w-4 ml-2" />
                  تعديل
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-red-600 hover:text-red-800"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDelete(group.images[0]);
                  }}
                >
                  <Trash2 className="h-4 w-4 ml-2" />
                  حذف
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Add/Edit Dialog */}
      <Dialog open={isAddDialogOpen || isEditDialogOpen} onOpenChange={isEditDialogOpen ? setIsEditDialogOpen : setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{selectedImage ? 'تعديل الصورة' : 'إضافة صور جديدة'}</DialogTitle>
            <DialogDescription>
              {selectedImage ? 'قم بتعديل بيانات الصورة' : 'قم بإضافة صورة واحدة أو أكثر مع البيانات المطلوبة'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              {!selectedImage && (
                <div className="space-y-2">
                  <Label>الصور</Label>
                  <FileUpload
                    onFileSelect={(files) => setUploadedFiles(files)}
                    multiple={true}
                  />
                </div>
              )}
              <div className="space-y-2">
                <Label htmlFor="title">عنوان المجموعة</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">وصف المجموعة</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">الفئة</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => setFormData({ ...formData, category: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر فئة الصور" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PREVIOUS_TRIP">رحلات سابقة</SelectItem>
                    <SelectItem value="GALLERY">معرض الصور</SelectItem>
                    <SelectItem value="NEWS">الأخبار</SelectItem>
                    <SelectItem value="PAGE_BACKGROUND">خلفيات الصفحات</SelectItem>
                    <SelectItem value="OTHER">أخرى</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter className="mt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => isEditDialogOpen ? setIsEditDialogOpen(false) : setIsAddDialogOpen(false)}
              >
                إلغاء
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                    جاري الحفظ...
                  </>
                ) : selectedImage ? 'حفظ التعديلات' : 'إضافة الصور'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد من حذف هذه الصورة؟</AlertDialogTitle>
            <AlertDialogDescription>
              هذا الإجراء لا يمكن التراجع عنه. سيتم حذف الصورة نهائياً.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              حذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Image Slider */}
      <ImageSlider
        images={sliderImages}
        open={isSliderOpen}
        onOpenChange={setIsSliderOpen}
      />
    </div>
  );
}