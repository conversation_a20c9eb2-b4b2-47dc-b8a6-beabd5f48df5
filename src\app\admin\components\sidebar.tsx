"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Home,
  Users,
  Settings,
  Package,
  FileText,
  CalendarCheck,
  LayoutDashboard,
  FileEdit,
  RadioTower,
  Bot,
  Newspaper,
  FileImage,
  Video,
  MessageSquare,
  HelpCircle,
  LogOut,
  ShieldCheck,
  User
} from "lucide-react";
import { useAuth } from "@/components/auth/useAuth";
import { useSiteSettings } from "@/hooks/use-site-settings";
import Image from "next/image";

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {}

export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname();
  const { user, logout } = useAuth();
  const { settings } = useSiteSettings();

  const routes = [
    {
      label: "لوحة التحكم الرئيسية",
      icon: LayoutDashboard,
      href: "/admin",
    },
    {
      label: "إدارة الصفحة الرئيسية",
      icon: FileEdit,
      href: "/admin/homepage-editor",
    },
    {
      label: "إدارة شريط الأخبار",
      icon: RadioTower,
      href: "/admin/news-ticker",
    },
    {
      label: "محسن المحتوى (AI)",
      icon: Bot,
      href: "/admin/content-optimizer",
    },
    {
      label: "إدارة الأخبار",
      icon: Newspaper,
      href: "/admin/news",
    },
    {
      label: "إدارة الصور",
      icon: FileImage,
      href: "/admin/media-images",
    },
    {
      label: "إدارة الفيديوهات",
      icon: Video,
      href: "/admin/media-videos",
    },
    {
      label: "إدارة الصفحات",
      icon: MessageSquare,
      href: "/admin/pages",
    },
    {
      label: "إدارة التعليقات",
      icon: MessageSquare,
      href: "/admin/comments",
    },
    {
      label: "إدارة الأسئلة الشائعة",
      icon: HelpCircle,
      href: "/admin/faqs",
    },
    {
      label: "إدارة المستخدمين",
      icon: Users,
      href: "/admin/pages/users",
    },
    {
      label: "طلبات الحج والعمرة",
      icon: CalendarCheck,
      href: "/admin/pages/bookings",
    },
    {
      label: "الإعدادات",
      icon: Settings,
      href: "/admin/settings",
    },
  ];

  return (
    <ScrollArea className={cn("h-full py-6", className)}>
      <div className="mb-8 px-6 flex justify-center">
        <Link href="/" className="block">
          {settings?.siteLogo && (
            <div className="relative w-16 h-16 mx-auto">
              <Image
                src={settings.siteLogo}
                alt="الشعار"
                fill
                className="object-contain"
                priority
              />
            </div>
          )}
        </Link>
      </div>
      <div className="space-y-1 px-3">
        {routes.map((route) => (
          <Link key={route.href} href={route.href}>
            <Button
              variant={pathname === route.href ? "secondary" : "ghost"}
              className="w-full justify-end text-right"
            >
              {route.label}
              <route.icon className="mr-2 h-5 w-5 rtl:ml-2 rtl:mr-0" />
            </Button>
          </Link>
        ))}
      </div>
    </ScrollArea>
  );
} 