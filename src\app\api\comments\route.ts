import { NextResponse } from 'next/server';
import { loadData, saveData } from '@/lib/data-utils';

const COMMENTS_FILE = 'comments.json';

export async function GET() {
  try {
    const comments = await loadData(COMMENTS_FILE, []);
    return NextResponse.json(comments, {
      headers: {
        'Cache-Control': 'no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    console.error('Error loading comments:', error);
    return NextResponse.json({ error: 'Failed to load comments' }, { status: 500 });
  }
}

export async function POST(req: Request) {
  const data = await req.json();
  const comments = await loadData(COMMENTS_FILE, []);
  
  // For new comment
  if (!data.id) {
    data.id = Date.now();
    comments.push(data);
  } else {
    // For updating existing comment
    const index = comments.findIndex((c: any) => c.id === data.id);
    if (index !== -1) {
      comments[index] = data;
    }
  }
  
  await saveData(COMMENTS_FILE, comments);
  return NextResponse.json(data);
}

export async function DELETE(req: Request) {
  const { id } = await req.json();
  const comments = await loadData(COMMENTS_FILE, []);
  const newComments = comments.filter((c: any) => c.id !== id);
  await saveData(COMMENTS_FILE, newComments);
  return NextResponse.json({ success: true });
} 