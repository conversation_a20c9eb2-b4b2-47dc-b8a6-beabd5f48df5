import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { unlink } from 'fs/promises';
import { join } from 'path';
import { MediaCategory } from '@prisma/client';

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { title, description, category } = await request.json();

    const media = await prisma.media.update({
      where: { id: params.id },
      data: {
        title,
        description,
        category: category as MediaCategory || undefined,
      },
    });

    return NextResponse.json(media);
  } catch (error) {
    console.error('Error updating media:', error);
    return NextResponse.json(
      { error: 'Error updating media' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // First get the media item to get its URL
    const media = await prisma.media.findUnique({
      where: { id: params.id }
    });

    if (!media) {
      return NextResponse.json(
        { error: 'Media not found' },
        { status: 404 }
      );
    }

    // Delete the file if it's in our uploads directory
    if (media.url.startsWith('/uploads/')) {
      const filePath = join(process.cwd(), 'public', media.url);
      try {
        await unlink(filePath);
      } catch (error) {
        console.error('Error deleting file:', error);
        // Continue with database deletion even if file deletion fails
      }
    }

    // Delete from database
    await prisma.media.delete({
      where: { id: params.id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting media:', error);
    return NextResponse.json(
      { error: 'Error deleting media' },
      { status: 500 }
    );
  }
} 