'use client';

import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import FloatingWhatsAppButton from '@/components/layout/FloatingWhatsAppButton';
import ScrollToTopButton from '@/components/layout/ScrollToTopButton';
import { Toaster } from "@/components/ui/toaster";

export default function RootLayoutContent({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-8">
        {children}
      </main>
      <Footer />
      <FloatingWhatsAppButton />
      <ScrollToTopButton />
      <Toaster />
    </div>
  );
} 